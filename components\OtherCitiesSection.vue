<template>
  <section v-if="heading && cities && cities.length" class="py-16">
    <div class="container mx-auto px-4">
      <SectionHeading 
        :title="heading"
        :subtitle="subheading"
      />
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
        <CityCard v-for="city in cities" :key="city.id" :city="city" />
      </div>
    </div>
  </section>
</template>

<script setup>
import SectionHeading from '~/components/atoms/SectionHeading.vue' // Assuming this path is correct
import CityCard from '~/components/CityCard.vue' // Assuming this path is correct

defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  cities: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>
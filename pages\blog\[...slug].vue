<template>
  <div>
    <article class="pt-32">
      <!-- Article Header -->
      <header class="container mx-auto px-4 mb-12">
        <div class="max-w-3xl mx-auto">
          <div class="flex items-center gap-2 mb-4">
            <span
              v-for="tag in article?.tags"
              :key="typeof tag === 'string' ? tag : tag.name"
              class="text-sm font-medium px-3 py-1 rounded-full bg-primary-50 text-primary-700"
            >
              {{ typeof tag === 'string' ? tag : tag.name }}
            </span>
          </div>
          <h1 class="text-4xl md:text-5xl font-bold mb-4">
            {{ article?.title }}
          </h1>
          <p class="text-xl text-gray-600 mb-6">
            {{ article?.description }}
          </p>
          <div class="flex items-center gap-6 text-sm text-gray-500">
            <span class="flex items-center">
              <Icon name="heroicons:calendar" class="w-4 h-4 mr-1" />
              {{ formatDate(article?.publishedAt || article?.date) }}
            </span>
            <span class="flex items-center">
              <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />
              {{ article?.readingTime }} min read
            </span>
          </div>
        </div>
      </header>

      <!-- Featured Image -->
      <div class="container mx-auto px-4 mb-12">
        <div class="max-w-4xl mx-auto">
          <img
            :src="article?.featuredImage || article?.image"
            :alt="article?.title"
            class="w-full h-[400px] object-cover rounded-xl"
          />
        </div>
      </div>

      <!-- Article Content -->
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto">
          <div class="prose prose-lg prose-primary mx-auto" v-html="article?.content">
            <!-- Content will be rendered here -->
          </div>
        </div>
      </div>

      <!-- Share Buttons -->
      <div class="container mx-auto px-4 mt-12">
        <div class="max-w-3xl mx-auto">
          <div class="flex items-center gap-4 border-t border-gray-200 pt-8">
            <span class="text-gray-600 font-medium">Share this article:</span>
            <div class="flex gap-2">
              <a 
                :href="`https://twitter.com/intent/tweet?text=${article.title}&url=${pageUrl}`"
                target="_blank"
                rel="noopener noreferrer"
                class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors duration-200"
              >
                <Icon name="mdi:twitter" />
              </a>
              <a 
                :href="`https://www.linkedin.com/shareArticle?mini=true&url=${pageUrl}&title=${article.title}`"
                target="_blank"
                rel="noopener noreferrer"
                class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors duration-200"
              >
                <Icon name="mdi:linkedin" />
              </a>
              <a 
                :href="`https://www.facebook.com/sharer/sharer.php?u=${pageUrl}`"
                target="_blank"
                rel="noopener noreferrer"
                class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors duration-200"
              >
                <Icon name="mdi:facebook" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </article>

    <!-- Related Articles -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">Related Articles</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <article v-for="relatedArticle in relatedArticles" :key="relatedArticle.id || relatedArticle._path" class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <NuxtLink :to="`/blog/${relatedArticle.slug || relatedArticle._path?.replace('/blog/', '')}`">
              <img
                :src="relatedArticle.featuredImage || relatedArticle.image"
                :alt="relatedArticle.title"
                class="w-full h-48 object-cover"
              />
              <div class="p-6">
                <div class="flex items-center gap-2 mb-3">
                  <span
                    v-for="tag in relatedArticle.tags"
                    :key="typeof tag === 'string' ? tag : tag.name"
                    class="text-xs font-medium px-2 py-1 rounded-full bg-primary-50 text-primary-700"
                  >
                    {{ typeof tag === 'string' ? tag : tag.name }}
                  </span>
                </div>
                <h3 class="text-xl font-bold mb-2 line-clamp-2 hover:text-primary-600 transition-colors duration-200">
                  {{ relatedArticle.title }}
                </h3>
                <p class="text-gray-600 mb-4 line-clamp-3">
                  {{ relatedArticle.description }}
                </p>
                <div class="flex items-center justify-between text-sm text-gray-500">
                  <span>{{ formatDate(relatedArticle.publishedAt || relatedArticle.date) }}</span>
                  <span class="flex items-center">
                    <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />
                    {{ relatedArticle.readingTime }} min read
                  </span>
                </div>
              </div>
            </NuxtLink>
          </article>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
const route = useRoute()
const slug = Array.isArray(route.params.slug) ? route.params.slug.join('/') : route.params.slug

// Fetch blog post data
const { data: blogData } = await useAsyncData(`blog-post-${slug}`, () =>
  $fetch(`/api/blog/${slug}`)
)

const article = computed(() => blogData.value?.data)
const relatedArticles = computed(() => blogData.value?.relatedPosts || [])

const pageUrl = computed(() => {
  if (import.meta.client) {
    return window.location.href
  }
  return ''
})

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// SEO Meta
useHead(() => ({
  title: `${article.value?.title || 'Blog Post'} | Gemilang VO Blog`,
  meta: [
    { name: 'description', content: article.value?.description || '' },
    { property: 'og:title', content: article.value?.title || '' },
    { property: 'og:description', content: article.value?.description || '' },
    { property: 'og:image', content: article.value?.featuredImage || article.value?.image || '' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: article.value?.title || '' },
    { name: 'twitter:description', content: article.value?.description || '' },
    { name: 'twitter:image', content: article.value?.featuredImage || article.value?.image || '' }
  ]
}))
</script>
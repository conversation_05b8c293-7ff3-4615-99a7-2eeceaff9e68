<template>
  <section v-if="plans && plans.length > 0" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Paket Virtual Office {{ cityName }}
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          <PERSON><PERSON>h paket yang sesuai dengan kebutuhan bisnis Anda. Semua paket sudah termasuk alamat bisnis premium dan layanan profesional.
        </p>
      </div>

      <!-- Pricing Cards Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
        <div v-for="plan in plans" :key="plan.id"
             class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200 hover:shadow-2xl transition-all duration-300"
             :class="{ 'ring-2 ring-primary-500 ring-opacity-50': plan.isBestValue }">

          <!-- Best Value Badge -->
          <div v-if="plan.isBestValue" class="bg-primary-500 text-white text-center py-2 text-sm font-semibold">
            PALING POPULER
          </div>

          <!-- Plan Header -->
          <div class="bg-primary-600 text-white p-6 text-center">
            <h3 class="text-xl font-bold mb-2">{{ plan.name }}</h3>
            <div class="text-3xl font-bold mb-1">{{ formatPrice(plan.price) }}</div>
            <div class="text-sm opacity-90">{{ plan.periodText }}</div>
            <div v-if="plan.excludePPN" class="text-xs opacity-75 mt-1">(exclude PPN)</div>
          </div>

          <!-- Plan Description -->
          <div class="p-6">
            <p class="text-gray-600 mb-6 text-center">{{ plan.description }}</p>

            <!-- Features List -->
            <ul class="space-y-3 mb-8">
              <li v-for="feature in plan.features" :key="feature" class="flex items-start">
                <Icon name="heroicons:check-circle" class="w-5 h-5 text-primary-500 mr-3 mt-0.5 flex-shrink-0" />
                <span class="text-sm text-gray-700">{{ feature }}</span>
              </li>
            </ul>

            <!-- WhatsApp Button -->
            <div class="text-center">
              <AppButton
                :href="getWhatsappLink(plan)"
                whatsapp
                variant="primary"
                size="lg"
                class="w-full font-bold py-3 transition-all duration-200 hover:scale-105"
              >
                Pilih {{ plan.name }}
              </AppButton>
            </div>
          </div>
        </div>
      </div>

      <!-- General Contact Section -->
      <div class="mt-12 text-center">
        <div class="bg-primary-600 rounded-xl p-8 text-white shadow-xl">
          <h3 class="text-2xl font-bold mb-3">Butuh Konsultasi Lebih Lanjut?</h3>
          <p class="mb-6 text-lg opacity-90">Tim ahli kami siap membantu Anda memilih paket yang tepat untuk kebutuhan bisnis Anda</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <AppButton
              :href="whatsappLink"
              whatsapp
              variant="primary"
              size="lg"
              class="font-bold py-3 transition-all duration-200 hover:scale-105"
            >
              Konsultasi Gratis
            </AppButton>
            <div class="text-sm opacity-75">
              Respon cepat dalam 5 menit
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  plans: {
    type: Array,
    required: true
  },
  cityName: {
    type: String,
    default: ''
  }
})

// WhatsApp link with dynamic city name for general consultation
const whatsappLink = computed(() => {
  const message = `Halo, saya tertarik dengan layanan Virtual Office di ${props.cityName || 'kota ini'}. Bisakah saya mendapatkan informasi lebih lanjut tentang paket dan harga?`;
  return `https://wa.me/6287799088880?text=${encodeURIComponent(message)}`;
});

// WhatsApp link for specific plan
const getWhatsappLink = (plan) => {
  const message = plan.whatsappMessage || `Halo, saya tertarik dengan paket ${plan.name} untuk Virtual Office di ${props.cityName || 'kota ini'}. Bisakah saya mendapatkan informasi lebih detail?`;
  return `https://wa.me/6287799088880?text=${encodeURIComponent(message)}`;
};

// Format price for better display
const formatPrice = (price) => {
  if (!price) return ''
  // Format number with thousands separator
  return `Rp ${price.toLocaleString('id-ID')}`
}


</script>

// Import the JSON file directly
import perijinanData from '~/data/perijinan.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Perijinan data loaded successfully');
    return perijinanData;
  } catch (error) {
    console.error('Error loading perijinan data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load perijinan data',
    });
  }
});
<template>
  <div class="bg-primary-800 p-6 rounded-xl">
    <div class="flex items-center mb-4">
      <div class="text-yellow-400">
        <Icon name="heroicons:star-solid" class="inline-block" v-for="i in 5" :key="i" />
      </div>
    </div>
    <p class="mb-6 italic">{{ testimonial.content }}</p>
    <div class="flex items-center">
      <div class="mr-4">
        <div class="w-12 h-12 rounded-full bg-primary-700 flex items-center justify-center">
          <span class="text-xl font-bold">{{ testimonial.author?.[0] || 'U' }}</span>
        </div>
      </div>
      <div>
        <h4 class="font-semibold">{{ testimonial.author }}</h4>
        <p class="text-primary-300 text-sm">{{ testimonial.role }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  testimonial: {
    type: Object,
    required: true
  }
})
</script>
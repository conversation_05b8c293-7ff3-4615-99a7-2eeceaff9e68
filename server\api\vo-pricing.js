import fs from 'fs'
import path from 'path'

export default defineEventHandler(async (event) => {
  try {
    const filePath = path.join(process.cwd(), 'data', 'voPricingPlans.json')
    const fileContent = fs.readFileSync(filePath, 'utf-8')
    const pricingData = JSON.parse(fileContent)
    
    console.log('VO Pricing data loaded successfully')
    
    return pricingData
  } catch (error) {
    console.error('Error loading VO pricing data:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to load VO pricing data'
    })
  }
})

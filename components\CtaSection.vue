<template>
  <section v-if="heading" class="py-20" aria-labelledby="cta-heading">
    <div class="container mx-auto px-4 max-w-4xl">
      <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <div class="flex flex-col md:flex-row">
          <div class="md:w-1/2 p-10 md:p-12">
            <h2 id="cta-heading" class="text-3xl font-bold mb-4">
              {{ heading }}
            </h2>
            <p v-if="subheading" class="text-gray-600 mb-6">{{ subheading }}</p>
            <AppButton v-if="primaryButtonText" size="lg">{{ primaryButtonText }}</AppButton>
          </div>
          <div class="md:w-1/2 bg-primary-600 p-10 md:p-12 text-white">
            <h3 v-if="formHeading" class="text-2xl font-bold mb-4">{{ formHeading }}</h3>
            <form class="space-y-4">
              <div>
                <label class="block text-sm font-medium mb-1" for="cta-name">Nama</label>
                <input type="text" id="cta-name" class="w-full px-3 py-2 text-gray-900 rounded-md focus:ring-2 focus:ring-primary-300 focus:outline-none" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1" for="cta-email">Email</label>
                <input type="email" id="cta-email" class="w-full px-3 py-2 text-gray-900 rounded-md focus:ring-2 focus:ring-primary-300 focus:outline-none" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1" for="cta-message">Pesan</label>
                <textarea id="cta-message" rows="3" class="w-full px-3 py-2 text-gray-900 rounded-md focus:ring-2 focus:ring-primary-300 focus:outline-none"></textarea>
              </div>
              <AppButton variant="outline" class="text-white border-white hover:bg-white/10 w-full">
                Kirim Pesan
              </AppButton>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// Assuming AppButton is globally registered or imported if needed
// import AppButton from '~/components/atoms/AppButton.vue'; 

defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  primaryButtonText: {
    type: String,
    default: ''
  },
  formHeading: {
    type: String,
    default: ''
  }
})
</script>
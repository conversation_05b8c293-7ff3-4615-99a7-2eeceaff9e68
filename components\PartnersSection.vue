<template>
  <section v-if="partners && partners.length" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{{ heading }}</h2>
        <p v-if="subheading" class="text-lg text-gray-600 max-w-2xl mx-auto">{{ subheading }}</p>
      </div>

      <!-- Partners Logos -->
      <div class="relative overflow-hidden">
        <!-- Scrolling Container -->
        <div 
          class="flex space-x-8 md:space-x-12 lg:space-x-16 animate-scroll"
          :style="{ width: `${partners.length * 200}px` }"
        >
          <!-- First Set of Logos -->
          <div 
            v-for="(partner, index) in partners" 
            :key="`first-${index}`"
            class="flex-shrink-0 w-32 md:w-40 lg:w-48 h-16 md:h-20 lg:h-24 flex items-center justify-center bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"
          >
            <img 
              :src="partner.logo" 
              :alt="partner.name"
              class="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 opacity-70 group-hover:opacity-100"
              loading="lazy"
            />
          </div>
          
          <!-- Duplicate Set for Seamless Loop -->
          <div 
            v-for="(partner, index) in partners" 
            :key="`second-${index}`"
            class="flex-shrink-0 w-32 md:w-40 lg:w-48 h-16 md:h-20 lg:h-24 flex items-center justify-center bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"
          >
            <img 
              :src="partner.logo" 
              :alt="partner.name"
              class="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 opacity-70 group-hover:opacity-100"
              loading="lazy"
            />
          </div>
        </div>
      </div>

      <!-- Static Grid for Mobile (Alternative) -->
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6 mt-8 lg:hidden">
        <div 
          v-for="(partner, index) in partners.slice(0, 6)" 
          :key="`static-${index}`"
          class="flex items-center justify-center bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-4 group"
        >
          <img 
            :src="partner.logo" 
            :alt="partner.name"
            class="max-w-full max-h-12 object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 opacity-70 group-hover:opacity-100"
            loading="lazy"
          />
        </div>
      </div>

      <!-- Trust Indicators -->
      <div class="text-center mt-12">
        <p class="text-sm text-gray-500">
          Dipercaya oleh <span class="font-semibold text-primary-600">{{ partners.length }}+</span> perusahaan terkemuka
        </p>
      </div>
    </div>
  </section>
</template>

<script setup>
const props = defineProps({
  heading: {
    type: String,
    default: 'Our Partners'
  },
  subheading: {
    type: String,
    default: 'Dipercaya oleh perusahaan-perusahaan terkemuka di Indonesia'
  },
  partners: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

/* Pause animation on hover */
.animate-scroll:hover {
  animation-play-state: paused;
}

/* Hide scrolling version on mobile, show static grid */
@media (max-width: 1023px) {
  .animate-scroll {
    display: none;
  }
}

/* Show scrolling version on desktop, hide static grid */
@media (min-width: 1024px) {
  .lg\:hidden {
    display: none !important;
  }
}
</style>

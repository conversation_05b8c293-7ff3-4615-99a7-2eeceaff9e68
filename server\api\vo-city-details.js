// Import the JSON file directly
import voCitiesData from '~/data/voCities.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('VO city details data loaded successfully');
    return voCitiesData;
  } catch (error) {
    console.error('Error loading VO city details data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load VO city details data.',
    });
  }
});
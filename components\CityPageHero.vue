<template>
  <div v-if="headline" class="relative">
    <div class="absolute inset-0 z-0">
      <img 
        :src="backgroundImageUrl" 
        :alt="headline" 
        class="w-full h-full object-cover"
      />
      <div class="absolute inset-0 bg-black/60"></div>
    </div>
    
    <div class="relative z-10 pt-32 pb-20">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl animate-slide-up">
          <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            {{ headline }}
          </h1>
          <p v-if="subheadline" class="text-xl text-gray-200 mb-6">
            {{ subheadline }}
          </p>
          <NuxtLink v-if="primaryButtonText && primaryButtonLink" :to="primaryButtonLink">
            <AppButton size="lg">{{ primaryButtonText }}</AppButton>
          </NuxtLink>
          <AppButton v-else-if="primaryButtonText" size="lg">{{ primaryButtonText }}</AppButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Assuming AppButton is globally registered or imported if needed
// import AppButton from '~/components/atoms/AppButton.vue';

defineProps({
  backgroundImageUrl: {
    type: String,
    required: true
  },
  headline: {
    type: String,
    required: true
  },
  subheadline: {
    type: String,
    default: ''
  },
  primaryButtonText: {
    type: String,
    default: ''
  },
  primaryButtonLink: {
    type: String,
    default: ''
  }
})
</script>
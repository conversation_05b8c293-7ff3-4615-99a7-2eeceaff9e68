// Import the JSON file directly
import logoRegistrationData from '~/data/pendaftaran-merek.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Logo registration data loaded successfully');
    return logoRegistrationData;
  } catch (error) {
    console.error('Error loading logo registration data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load logo registration data',
    });
  }
})
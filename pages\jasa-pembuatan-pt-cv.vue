<template>
  <div>
    <!-- Hero Section -->
    <HeroSectionWithStats
      v-if="heroSectionData"
      :backgroundImageUrl="heroSectionData.backgroundImageUrl"
      :headline="heroSectionData.headline"
      :subheadline="heroSectionData.subheadline"
      :primaryButtonText="heroSectionData.primaryButtonText"
      :primaryButtonLink="heroSectionData.primaryButtonLink"
      :primaryButtonWhatsapp="heroSectionData.primaryButtonWhatsapp"
      :secondaryButtonText="heroSectionData.secondaryButtonText"
      :secondaryButtonLink="heroSectionData.secondaryButtonLink"
      :stats="heroSectionData.stats"
    />
    
    <!-- Business Types Section -->
    <BusinessTypesSection
      v-if="businessTypesSectionData"
      :heading="businessTypesSectionData.heading"
      :subheading="businessTypesSectionData.subheading"
      :types="businessTypesSectionData.types"
    />
    
    <!-- Services Section -->
    <ServicesGridSection
      v-if="servicesSectionData"
      sectionId="services"
      badgeText="Layanan Kami"
      :heading="servicesSectionData.heading"
      :subheading="servicesSectionData.subheading"
      :services="servicesSectionData.services"
      buttonText="Pelajari Lebih Lanjut"
    />

    <!-- Process Section -->
    <ProcessStepsSection
      v-if="processSectionData"
      badgeText="Proses Kerja"
      :heading="processSectionData.heading"
      :subheading="processSectionData.subheading"
      :steps="processSectionData.steps"
    />

    <!-- Pricing Section -->
    <PricingPlansSection
      v-if="pricingSectionData"
      badgeText="Harga Layanan"
      :heading="pricingSectionData.heading"
      :subheading="pricingSectionData.subheading"
      :plans="pricingSectionData.plans"
      buttonText="Pilih Paket"
      whatsapp-number="6287799088880"
      service-name="Jasa Pembuatan PT/CV"
    />

    <!-- FAQ Section -->
    <FaqSection
      v-if="faqSectionData"
      :heading="faqSectionData.heading"
      :subheading="faqSectionData.subheading"
      :faqs="faqSectionData.faqs"
    />

    <!-- CTA Section -->
    <CtaContactSection
      v-if="ctaSectionData"
      :heading="ctaSectionData.heading"
      :subheading="ctaSectionData.subheading"
      :benefits="ctaSectionData.benefits"
      :button-text="ctaSectionData.primaryButtonText"
      whatsapp-link="https://wa.me/6287799088880?text=Halo,%20saya%20tertarik%20dengan%20layanan%20Pembuatan%20PT/CV"
      :form-heading="ctaSectionData.formHeading"
      :operational-hours="ctaSectionData.operationalHours"
      :contact-info="ctaSectionData.contactInfo"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import HeroSectionWithStats from '~/components/HeroSectionWithStats.vue'
import BusinessTypesSection from '~/components/BusinessTypesSection.vue'
import ServicesGridSection from '~/components/ServicesGridSection.vue'
import ProcessStepsSection from '~/components/ProcessStepsSection.vue'
import PricingPlansSection from '~/components/PricingPlansSection.vue'
import FaqSection from '~/components/FaqSection.vue'
import CtaContactSection from '~/components/CtaContactSection.vue'

// Load data from API
const { data: companyFormationData } = await useFetch('/api/company-formation')

// For debugging
console.log('API Data:', companyFormationData.value)

// Use API data directly
const pageData = computed(() => companyFormationData.value || {})

// Extract section data
const heroSectionData = computed(() => pageData.value.heroSection)
const businessTypesSectionData = computed(() => pageData.value.businessTypesSection)
const servicesSectionData = computed(() => pageData.value.servicesSection)
const processSectionData = computed(() => pageData.value.processSection)
const pricingSectionData = computed(() => pageData.value.pricingSection)
const faqSectionData = computed(() => pageData.value.faqSection)
const ctaSectionData = computed(() => pageData.value.ctaSection)

// SEO Configuration
import { generateServiceSchema, generateWebPageSchema } from '~/utils/seo.js'

const config = useRuntimeConfig()
const baseUrl = config.public.siteUrl.replace(/\/$/, '') // Remove trailing slash
const pageUrl = `${baseUrl}/jasa-pembuatan-pt-cv`

const pageSchemas = computed(() => [
  generateServiceSchema(
    'Jasa Pembuatan PT dan CV',
    'Layanan pendirian PT dan CV profesional dengan proses cepat, legal, dan terpercaya. Pendampingan penuh dari tim ahli berpengalaman.',
    baseUrl
  ),
  generateWebPageSchema(
    'Jasa Pembuatan PT dan CV Professional | Gemilang VO',
    'Layanan pendirian PT dan CV profesional. Proses cepat, legal, dan terpercaya dengan pendampingan penuh dari tim ahli.',
    pageUrl,
    baseUrl
  )
])

useHead({
  title: 'Jasa Pembuatan PT dan CV Professional | Gemilang VO',
  meta: [
    { name: 'description', content: 'Layanan pendirian PT dan CV profesional. Proses cepat, legal, dan terpercaya dengan pendampingan penuh dari tim ahli.' },
    { name: 'keywords', content: 'jasa pembuatan PT, pendirian CV, badan usaha, legalitas perusahaan, akta pendirian, notaris, izin usaha' },
    { name: 'robots', content: 'index,follow' },
    { property: 'og:title', content: 'Jasa Pembuatan PT dan CV Professional | Gemilang VO' },
    { property: 'og:description', content: 'Layanan pendirian PT dan CV profesional. Proses cepat, legal, dan terpercaya dengan pendampingan penuh dari tim ahli.' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: pageUrl },
    { property: 'og:site_name', content: 'Gemilang VO' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'Jasa Pembuatan PT dan CV Professional | Gemilang VO' },
    { name: 'twitter:description', content: 'Layanan pendirian PT dan CV profesional. Proses cepat, legal, dan terpercaya dengan pendampingan penuh dari tim ahli.' }
  ],
  link: [
    { rel: 'canonical', href: pageUrl }
  ],
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify(pageSchemas.value)
    }
  ]
})
</script>

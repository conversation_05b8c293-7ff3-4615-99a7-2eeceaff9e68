// Import the JSON file directly
import meetingRoomData from '~/data/sewa-ruang-meeting.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Meeting room data loaded successfully');
    return meetingRoomData;
  } catch (error) {
    console.error('Error loading meeting room data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load meeting room data',
    });
  }
});
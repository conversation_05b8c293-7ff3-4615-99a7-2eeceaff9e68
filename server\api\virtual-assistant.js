// Import the JSON file directly
import assistantData from '~/data/asisten-virtual.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Virtual assistant data loaded successfully');
    return assistantData;
  } catch (error) {
    console.error('Error loading virtual assistant data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load virtual assistant data',
    });
  }
});
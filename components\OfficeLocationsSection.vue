<template>
  <section v-if="heading && cities.length" class="py-16 bg-gray-50" aria-labelledby="locations-heading">
    <div class="container mx-auto px-4">
      <h2 id="locations-heading" class="text-3xl md:text-4xl font-bold text-center mb-4">
        {{ heading }}
      </h2>
      <p v-if="subheading" class="text-gray-600 text-center max-w-2xl mx-auto mb-12">
        {{ subheading }}
      </p>
      
      <div class="flex justify-center items-center pb-6 space-x-6 relative">
        <CityCard
          v-for="(city, index) in displayedCities"
          :key="city.id"
          :city="city"
          :class="[
            'flex-shrink-0 w-full sm:w-[280px] md:w-[300px]',
            index === 0 || index === 3 ? 'mt-0' : 'mt-28'
          ]"
        />
      </div>
      
      <div class="text-center mt-8">
        <NuxtLink
          to="/cities"
          class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-300"
        >
          Check All Offices
          <Icon name="heroicons:arrow-right" class="ml-2 h-5 w-5" />
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script setup>
import CityCard from '~/components/CityCard.vue'
import { computed } from 'vue'

const props = defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  cities: {
    type: Array,
    required: true,
    default: () => []
  }
})

// Only display the first 4 cities
const displayedCities = computed(() => {
  return props.cities.slice(0, 4)
})
</script>
<template>
  <div>
    <!-- Hero Section -->
    <HeroSection
      v-if="heroSectionData"
      :backgroundImageUrl="heroSectionData.backgroundImageUrl"
      :headline="heroSectionData.headline"
      :subheadline="heroSectionData.subheadline"
      :primaryButtonText="heroSectionData.primaryButtonText"
      :primaryButtonLink="heroSectionData.primaryButtonLink"
      :primaryButtonWhatsapp="heroSectionData.primaryButtonWhatsapp"
      :secondaryButtonText="heroSectionData.secondaryButtonText"
      :secondaryButtonLink="heroSectionData.secondaryButtonLink"
    />
    
    <!-- Why Choose Section -->
    <WhyChooseSection
      v-if="whyChooseData"
      :heading="whyChooseData.heading"
      :subheading="whyChooseData.subheading"
      :features="whyChooseData.features"
    />
    
    <!-- Services Section -->
    <ServicesGridSection
      v-if="servicesSectionData"
      sectionId="services"
      badgeText="<PERSON>ana<PERSON>"
      :heading="servicesSectionData.heading"
      :subheading="servicesSectionData.subheading"
      :services="servicesSectionData.services"
      buttonText="Pelajari Lebih Lanjut"
    />

    <!-- Process Section -->
    <ProcessStepsSection
      v-if="processSectionData"
      badgeText="Proses Pendaftaran"
      :heading="processSectionData.heading"
      :subheading="processSectionData.subheading"
      :steps="processSectionData.steps"
    />

    <!-- Pricing Section -->
    <PricingPlansSection
      v-if="pricingSectionData"
      badgeText="Harga Layanan"
      :heading="pricingSectionData.heading"
      :subheading="pricingSectionData.subheading"
      :plans="pricingSectionData.plans"
      buttonText="Pilih Paket"
      whatsapp-number="6287799088880"
      service-name="Jasa Pendaftaran Logo"
    />

    <!-- Testimonials Section -->
    <TestimonialsSection
      v-if="testimonialsSectionData"
      :heading="testimonialsSectionData.heading"
      :subheading="testimonialsSectionData.subheading"
      :testimonials="testimonialsSectionData.testimonials"
    />

    <!-- FAQ Section -->
    <FaqSection
      v-if="faqSectionData"
      :heading="faqSectionData.heading"
      :subheading="faqSectionData.subheading"
      :faqs="faqSectionData.faqs"
    />

    <!-- CTA Section -->
    <CtaContactSection 
      v-if="ctaSectionData"
      :heading="ctaSectionData.heading"
      :subheading="ctaSectionData.subheading"
      :benefits="ctaSectionData.benefits"
      :button-text="ctaSectionData.primaryButtonText"
      whatsapp-link="https://wa.me/6287799088880?text=Halo,%20saya%20tertarik%20dengan%20layanan%20Pendaftaran%20Logo"
      :form-heading="ctaSectionData.formHeading"
      :operational-hours="ctaSectionData.operationalHours"
      :contact-info="ctaSectionData.contactInfo"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import HeroSection from '~/components/HeroSection.vue'
import WhyChooseSection from '~/components/WhyChooseSection.vue'
import ServicesGridSection from '~/components/ServicesGridSection.vue'
import ProcessStepsSection from '~/components/ProcessStepsSection.vue'
import PricingPlansSection from '~/components/PricingPlansSection.vue'
import TestimonialsSection from '~/components/TestimonialsSection.vue'
import FaqSection from '~/components/FaqSection.vue'
import CtaContactSection from '~/components/CtaContactSection.vue'

// Load data from API
const { data: logoRegistrationData } = await useFetch('/api/logo-registration')

// For debugging
console.log('API Data:', logoRegistrationData.value)

// Use API data directly
const pageData = computed(() => logoRegistrationData.value || {})

// Extract section data
const heroSectionData = computed(() => pageData.value.heroSection)
const whyChooseData = computed(() => pageData.value.whyChoose)
const servicesSectionData = computed(() => pageData.value.servicesSection)
const processSectionData = computed(() => pageData.value.processSection)
const pricingSectionData = computed(() => pageData.value.pricingSection)
const testimonialsSectionData = computed(() => pageData.value.testimonialsSection)
const faqSectionData = computed(() => pageData.value.faqSection)
const ctaSectionData = computed(() => pageData.value.ctaSection)

// SEO Configuration
import { generateServiceSchema, generateWebPageSchema } from '~/utils/seo.js'

const config = useRuntimeConfig()
const baseUrl = config.public.siteUrl.replace(/\/$/, '') // Remove trailing slash
const pageUrl = `${baseUrl}/jasa-pendaftaran-logo`

const pageSchemas = computed(() => [
  generateServiceSchema(
    'Jasa Pendaftaran Logo & Merek',
    'Layanan pendaftaran merek dagang profesional. Lindungi merek bisnis Anda dengan pendaftaran yang legal dan terpercaya.',
    baseUrl
  ),
  generateWebPageSchema(
    'Jasa Pendaftaran Logo & Merek | Gemilang VO',
    'Layanan pendaftaran merek dagang profesional. Lindungi merek bisnis Anda dengan pendaftaran yang legal dan terpercaya.',
    pageUrl,
    baseUrl
  )
])

useHead({
  title: 'Jasa Pendaftaran Logo & Merek | Gemilang VO',
  meta: [
    { name: 'description', content: 'Layanan pendaftaran merek dagang profesional. Lindungi merek bisnis Anda dengan pendaftaran yang legal dan terpercaya.' },
    { name: 'keywords', content: 'jasa pendaftaran merek, trademark, logo, merek dagang, HAKI, intellectual property, brand protection' },
    { name: 'robots', content: 'index,follow' },
    { property: 'og:title', content: 'Jasa Pendaftaran Logo & Merek | Gemilang VO' },
    { property: 'og:description', content: 'Layanan pendaftaran merek dagang profesional. Lindungi merek bisnis Anda dengan pendaftaran yang legal dan terpercaya.' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: pageUrl },
    { property: 'og:site_name', content: 'Gemilang VO' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'Jasa Pendaftaran Logo & Merek | Gemilang VO' },
    { name: 'twitter:description', content: 'Layanan pendaftaran merek dagang profesional. Lindungi merek bisnis Anda dengan pendaftaran yang legal dan terpercaya.' }
  ],
  link: [
    { rel: 'canonical', href: pageUrl }
  ],
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify(pageSchemas.value)
    }
  ]
})
</script>
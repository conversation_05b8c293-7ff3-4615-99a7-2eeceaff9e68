// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: false },
  modules: [
    '@nuxtjs/tailwindcss',
    'nuxt-icon'
  ],
  runtimeConfig: {
    // Private keys (only available on server-side)
    strapiUrl: process.env.STRAPI_URL || 'http://localhost:1337',
    strapiApiToken: process.env.STRAPI_API_TOKEN || '',

    // Public keys (exposed to client-side)
    public: {
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'https://sewavirtualoffice.id/',
      twitterHandle: process.env.TWITTER_HANDLE || '@gemilangvo',
      facebookPage: process.env.FACEBOOK_PAGE || 'https://facebook.com/gemilangvo',
      instagramPage: process.env.INSTAGRAM_PAGE || 'https://www.instagram.com/virtualoffice.joglosemar/',
      linkedinPage: process.env.LINKEDIN_PAGE || 'https://linkedin.com/company/gemilangvo',
      companyPhone: process.env.COMPANY_PHONE || '+62-877-9908-8880',
      companyAddress: process.env.COMPANY_ADDRESS || 'Jl. Sukun Mataram Bumi Sejahtera No.3, Ngringin, Condongcatur, Kec. Depok, Kabupaten Sleman, Daerah Istimewa Yogyakarta 55281'
    }
  },
  app: {
    head: {
      title: 'Virtual Office Solutions',
      meta: [
        { name: 'description', content: 'Premium virtual office services across Indonesia' }
      ],
      link: [
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap' }
      ]
    }
  }
})
<template>
  <section v-if="heading && services.length" class="py-20" aria-labelledby="services-heading">
    <div class="container mx-auto px-4">
      <h2 id="services-heading" class="text-3xl md:text-4xl font-bold text-center mb-4">
        {{ heading }}
      </h2>
      <p v-if="subheading" class="text-gray-600 text-center max-w-2xl mx-auto mb-12">
        {{ subheading }}
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <ServiceItemCard v-for="service in services" :key="service.id" :service="service" />
      </div>
    </div>
  </section>
</template>

<script setup>
import ServiceItemCard from '~/components/ServiceItemCard.vue'

defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  services: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>
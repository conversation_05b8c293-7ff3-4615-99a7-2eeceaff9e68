# Panduan Integrasi Strapi

Dokumen ini berisi panduan untuk mengintegrasikan aplikasi Nuxt.js dengan Strapi CMS. Berikut adalah perubahan yang diperlukan untuk setiap API route.

## Konfigurasi Strapi

Tambahkan file konfigurasi Strapi di project Anda:

```javascript
// utils/strapi.js
export const STRAPI_URL = 'https://your-strapi-url.com/api';

export async function fetchFromStrapi(endpoint, options = {}) {
  try {
    const url = `${STRAPI_URL}/${endpoint}`;
    const response = await $fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    return response;
  } catch (error) {
    console.error(`Error fetching from Strapi (${endpoint}):`, error);
    throw error;
  }
}
```

## API Routes

### 1. server/api/home.js

**Sebelum:**
```javascript
// Import the JSON file directly
import homeData from '~/data/home.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('API Home Data loaded successfully');
    return homeData;
  } catch (error) {
    console.error('Error loading home data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load homepage data',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('home');
    
    // Transform data if needed to match expected structure
    const transformedData = {
      heroSection: response.data.attributes.heroSection,
      whyChoose: response.data.attributes.whyChoose,
      officeLocationsSection: response.data.attributes.officeLocationsSection,
      servicesSection: response.data.attributes.servicesSection,
      faqSection: response.data.attributes.faqSection,
      testimonialsSection: response.data.attributes.testimonialsSection,
      ctaSection: response.data.attributes.ctaSection,
      newCta: response.data.attributes.newCta
    };
    
    console.log('API Home Data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading home data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load homepage data',
    });
  }
});
```

### 2. server/api/services.js

**Sebelum:**
```javascript
// Import the JSON file directly
import servicesData from '~/data/services.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('API Services Data loaded successfully');
    return servicesData;
  } catch (error) {
    console.error('Error loading services data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load services data',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('services');
    
    // Transform data explicitly like in homepage
    const transformedData = {
      services: response.data.map(item => ({
        id: item.id,
        name: item.attributes.name,
        description: item.attributes.description,
        icon: item.attributes.icon,
        path: item.attributes.path,
        image: item.attributes.image
      }))
    };
    
    console.log('API Services Data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading services data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load services data',
    });
  }
});
```

### 3. server/api/cities.js

**Sebelum:**
```javascript
export default defineEventHandler(async (event) => {
  return {
    cities: [
      // Array of city objects
    ]
  }
})
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('cities');
    
    // Transform data if needed
    const transformedData = {
      cities: response.data.map(city => ({
        id: city.attributes.slug,
        name: city.attributes.name,
        image: city.attributes.image,
        description: city.attributes.description,
        address: city.attributes.address,
        mapUrl: city.attributes.mapUrl,
        facilities: city.attributes.facilities,
        prices: city.attributes.prices,
        roomImages: city.attributes.roomImages
      }))
    };
    
    return transformedData;
  } catch (error) {
    console.error('Error loading cities data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load cities data',
    });
  }
});
```

### 4. server/api/vo-city-details.js

**Sebelum:**
```javascript
// Import the JSON file directly
import voCitiesData from '~/data/voCities.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('VO city details data loaded successfully');
    return voCitiesData;
  } catch (error) {
    console.error('Error loading VO city details data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load VO city details data.',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('vo-cities');
    
    // Transform data explicitly like in homepage
    const transformedData = response.data.map(city => ({
      slug: city.attributes.slug,
      name: city.attributes.name,
      hero: city.attributes.hero,
      locationInfo: city.attributes.locationInfo,
      pricingPlans: city.attributes.pricingPlans,
      relatedServices: city.attributes.relatedServices,
      faqSection: city.attributes.faqSection,
      otherCitiesSection: city.attributes.otherCitiesSection,
      meta: city.attributes.meta,
      roomImages: city.attributes.roomImages
    }));
    
    console.log('VO city details data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading VO city details data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load VO city details data.',
    });
  }
});
```

### 5. server/api/city-details/[slug].js

**Sebelum:**
```javascript
// Import the voCities.json file that contains all city data
import voCitiesData from '~/data/voCities.json';

// Create a map of all city data for quick lookup by slug
const cityDetailsMap = {};
voCitiesData.forEach(city => {
  cityDetailsMap[city.slug] = city;
});

export default defineEventHandler(async (event) => {
  const slug = event.context.params?.slug;

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'City slug is required',
    });
  }

  try {
    // Get the city data from our map
    const cityData = cityDetailsMap[slug];
    
    if (!cityData) {
      console.error(`City details not found for: ${slug}`);
      throw createError({
        statusCode: 404,
        statusMessage: `Details for city '${slug}' not found.`,
      });
    }
    
    console.log(`City details data for ${slug} loaded successfully`);
    return cityData;
  } catch (error) {
    console.error(`Error loading city details for ${slug}:`, error);
    
    if (error.statusCode === 404) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load city details data.',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  const slug = event.context.params?.slug;

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'City slug is required',
    });
  }

  try {
    // Fetch specific city from Strapi using slug
    const response = await fetchFromStrapi(`vo-cities/${slug}`);
    
    if (!response.data) {
      console.error(`City details not found for: ${slug}`);
      throw createError({
        statusCode: 404,
        statusMessage: `Details for city '${slug}' not found.`,
      });
    }
    
    // Transform data explicitly like in homepage
    const cityData = {
      slug: response.data.attributes.slug,
      name: response.data.attributes.name,
      hero: response.data.attributes.hero,
      locationInfo: response.data.attributes.locationInfo,
      pricingPlans: response.data.attributes.pricingPlans,
      relatedServices: response.data.attributes.relatedServices,
      faqSection: response.data.attributes.faqSection,
      otherCitiesSection: response.data.attributes.otherCitiesSection,
      meta: response.data.attributes.meta,
      roomImages: response.data.attributes.roomImages
    };
    
    console.log(`City details data for ${slug} loaded successfully from Strapi`);
    return cityData;
  } catch (error) {
    console.error(`Error loading city details for ${slug} from Strapi:`, error);
    
    if (error.statusCode === 404) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load city details data.',
    });
  }
});
```

### 6. server/api/financial-report.js

**Sebelum:**
```javascript
// Import the JSON file directly
import reportData from '~/data/laporan.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Financial report data loaded successfully');
    return reportData;
  } catch (error) {
    console.error('Error loading financial report data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load financial report data',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('financial-report');
    
    // Transform data explicitly like in homepage
    const transformedData = {
      heroSection: response.data.attributes.heroSection,
      benefitsSection: response.data.attributes.benefitsSection,
      servicesSection: response.data.attributes.servicesSection,
      processSection: response.data.attributes.processSection,
      pricingSection: response.data.attributes.pricingSection,
      faqSection: response.data.attributes.faqSection,
      ctaSection: response.data.attributes.ctaSection
    };
    
    console.log('Financial report data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading financial report data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load financial report data',
    });
  }
});
```

### 7. server/api/company-formation.js

**Sebelum:**
```javascript
// Import the JSON file directly
import companyFormationData from '~/data/jasa-pembuatan-pt-cv.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Company formation data loaded successfully');
    return companyFormationData;
  } catch (error) {
    console.error('Error loading company formation data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load company formation data',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('company-formation');
    
    // Transform data explicitly like in homepage
    const transformedData = {
      heroSection: response.data.attributes.heroSection,
      benefitsSection: response.data.attributes.benefitsSection,
      servicesSection: response.data.attributes.servicesSection,
      processSection: response.data.attributes.processSection,
      pricingSection: response.data.attributes.pricingSection,
      faqSection: response.data.attributes.faqSection,
      ctaSection: response.data.attributes.ctaSection
    };
    
    console.log('Company formation data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading company formation data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load company formation data',
    });
  }
});
```

### 8. server/api/perijinan.js

**Sebelum:**
```javascript
// Import the JSON file directly
import perijinanData from '~/data/perijinan.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Perijinan data loaded successfully');
    return perijinanData;
  } catch (error) {
    console.error('Error loading perijinan data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load perijinan data',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('perijinan');
    
    // Transform data explicitly like in homepage
    const transformedData = {
      heroSection: response.data.attributes.heroSection,
      benefitsSection: response.data.attributes.benefitsSection,
      servicesSection: response.data.attributes.servicesSection,
      processSection: response.data.attributes.processSection,
      pricingSection: response.data.attributes.pricingSection,
      faqSection: response.data.attributes.faqSection,
      ctaSection: response.data.attributes.ctaSection
    };
    
    console.log('Perijinan data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading perijinan data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load perijinan data',
    });
  }
});
```

### 9. server/api/meeting-room.js

**Sebelum:**
```javascript
// Import the JSON file directly
import meetingRoomData from '~/data/sewa-ruang-meeting.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Meeting room data loaded successfully');
    return meetingRoomData;
  } catch (error) {
    console.error('Error loading meeting room data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load meeting room data',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('meeting-room');
    
    // Transform data explicitly like in homepage
    const transformedData = {
      heroSection: response.data.attributes.heroSection,
      benefitsSection: response.data.attributes.benefitsSection,
      servicesSection: response.data.attributes.servicesSection,
      processSection: response.data.attributes.processSection,
      pricingSection: response.data.attributes.pricingSection,
      faqSection: response.data.attributes.faqSection,
      ctaSection: response.data.attributes.ctaSection
    };
    
    console.log('Meeting room data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading meeting room data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load meeting room data',
    });
  }
});
```

### 10. server/api/logo-registration.js

**Sebelum:**
```javascript
// Import the JSON file directly
import logoRegistrationData from '~/data/pendaftaran-merek.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Logo registration data loaded successfully');
    return logoRegistrationData;
  } catch (error) {
    console.error('Error loading logo registration data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load logo registration data',
    });
  }
})
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('logo-registration');
    
    // Transform data explicitly like in homepage
    const transformedData = {
      heroSection: response.data.attributes.heroSection,
      benefitsSection: response.data.attributes.benefitsSection,
      servicesSection: response.data.attributes.servicesSection,
      processSection: response.data.attributes.processSection,
      pricingSection: response.data.attributes.pricingSection,
      faqSection: response.data.attributes.faqSection,
      ctaSection: response.data.attributes.ctaSection
    };
    
    console.log('Logo registration data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading logo registration data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load logo registration data',
    });
  }
})
```

### 11. server/api/virtual-assistant.js

**Sebelum:**
```javascript
// Import the JSON file directly
import assistantData from '~/data/asisten-virtual.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Virtual assistant data loaded successfully');
    return assistantData;
  } catch (error) {
    console.error('Error loading virtual assistant data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load virtual assistant data',
    });
  }
});
```

**Sesudah:**
```javascript
import { fetchFromStrapi } from '~/utils/strapi';

export default defineEventHandler(async (event) => {
  try {
    // Fetch data from Strapi API
    const response = await fetchFromStrapi('virtual-assistant');
    
    // Transform data explicitly like in homepage
    const transformedData = {
      heroSection: response.data.attributes.heroSection,
      benefitsSection: response.data.attributes.benefitsSection,
      servicesSection: response.data.attributes.servicesSection,
      processSection: response.data.attributes.processSection,
      pricingSection: response.data.attributes.pricingSection,
      faqSection: response.data.attributes.faqSection,
      ctaSection: response.data.attributes.ctaSection
    };
    
    console.log('Virtual assistant data loaded successfully from Strapi');
    return transformedData;
  } catch (error) {
    console.error('Error loading virtual assistant data from Strapi:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load virtual assistant data',
    });
  }
});
```

## Catatan Penting

1. Pastikan struktur data di Strapi sesuai dengan struktur data yang diharapkan oleh komponen frontend.

2. Jika struktur data di Strapi berbeda, gunakan transformasi data di API routes untuk memastikan komponen menerima data dalam format yang diharapkan.

3. Sesuaikan URL Strapi di `utils/strapi.js` dengan URL Strapi Anda yang sebenarnya.

4. Jika Strapi memerlukan autentikasi, tambahkan token API di header permintaan di fungsi `fetchFromStrapi`.

5. Lakukan migrasi satu per satu dan uji setiap endpoint sebelum melanjutkan ke endpoint berikutnya.
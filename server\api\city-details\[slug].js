// Import the voCities.json file that contains all city data
import voCitiesData from '~/data/voCities.json';

// Create a map of all city data for quick lookup by slug
const cityDetailsMap = {};
voCitiesData.forEach(city => {
  cityDetailsMap[city.slug] = city;
});

export default defineEventHandler(async (event) => {
  const slug = event.context.params?.slug;

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'City slug is required',
    });
  }

  try {
    // Get the city data from our map
    const cityData = cityDetailsMap[slug];
    
    if (!cityData) {
      console.error(`City details not found for: ${slug}`);
      throw createError({
        statusCode: 404,
        statusMessage: `Details for city '${slug}' not found.`,
      });
    }
    
    console.log(`City details data for ${slug} loaded successfully`);
    return cityData;
  } catch (error) {
    console.error(`Error loading city details for ${slug}:`, error);
    
    if (error.statusCode === 404) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load city details data.',
    });
  }
});
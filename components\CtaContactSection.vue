<template>
  <section class="py-20">
    <div class="container mx-auto px-4">
      <div class="max-w-5xl mx-auto bg-white rounded-2xl overflow-hidden shadow-xl">
        <div class="md:flex">
          <div class="md:w-1/2 p-12">
            <h2 class="text-3xl font-bold mb-6">
              {{ heading }}
            </h2>
            <p class="text-gray-600 mb-8">
              {{ subheading }}
            </p>
            <div class="space-y-4">
              <div v-for="(benefit, index) in benefits" :key="index" class="flex items-center">
                <Icon name="heroicons:check-circle" class="text-primary-500 w-5 h-5 mr-3" />
                <span>{{ benefit }}</span>
              </div>
            </div>
            <div class="mt-8">
              <AppButton 
                size="lg"
                :href="whatsappLink"
                whatsapp
              >
                {{ buttonText }}
              </AppButton>
            </div>
          </div>
          <div class="md:w-1/2 bg-primary-900 p-12 text-white">
            <h3 class="text-2xl font-bold mb-6">{{ formHeading }}</h3>
            <div class="space-y-4">
              <div v-for="(hour, index) in operationalHours" :key="index" class="flex justify-between items-center">
                <span>{{ hour.day }}</span>
                <span>{{ hour.hours }}</span>
              </div>
            </div>
            <div class="mt-8 pt-8 border-t border-white/20">
              <div v-for="(contact, index) in contactInfo" :key="index" class="flex items-center mb-4">
                <Icon :name="contact.icon" class="w-5 h-5 mr-3" />
                <span>{{ contact.info }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import AppButton from '~/components/atoms/AppButton.vue'

defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  benefits: {
    type: Array,
    default: () => []
  },
  buttonText: {
    type: String,
    default: 'Hubungi Kami'
  },
  whatsappLink: {
    type: String,
    default: 'https://wa.me/6287799088880?text=Halo,%20saya%20tertarik%20dengan%20layanan%20Anda'
  },
  formHeading: {
    type: String,
    default: 'Jam Operasional'
  },
  operationalHours: {
    type: Array,
    default: () => []
  },
  contactInfo: {
    type: Array,
    default: () => []
  }
})
</script>
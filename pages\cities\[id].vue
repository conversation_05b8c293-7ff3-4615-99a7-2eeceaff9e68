<template>
  <div v-if="cityPagePending">
    <div class="container mx-auto px-4 py-20 text-center">
      <p class="text-xl">Loading city details...</p>
      <!-- You can add a spinner or a more sophisticated loading state here -->
    </div>
  </div>
  <div v-else-if="cityPageError || !cityPageData">
    <div class="container mx-auto px-4 py-20 text-center">
      <h1 class="text-3xl font-bold mb-4">City Not Found</h1>
      <p class="text-gray-600">We couldn't find the details for this city. It might have been moved or does not exist.</p>
      <NuxtLink to="/cities" class="mt-8 inline-block px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
        View All Cities
      </NuxtLink>
    </div>
  </div>
  <div v-else>
    <CityPageHero
      v-if="heroData"
      :background-image-url="heroData.backgroundImageUrl"
      :headline="heroData.headline"
      :subheadline="heroData.subheadline"
      :primary-button-text="heroData.primaryButtonText"
      :primary-button-link="heroData.primaryButtonLink"
    />

    <CityDetailSection
      v-if="locationInfoData"
      :location-info="locationInfoData"
      :room-images="roomImagesData"
    />

    <CityPricingTable
      v-if="pricingPlansData"
      :pricing-plans="pricingPlansData"
      :city-name="cityPageData?.name || citySlug"
    />

    <RelatedServicesSection
      v-if="relatedServicesData"
      :heading="relatedServicesData.heading"
      :subheading="relatedServicesData.subheading"
      :services="relatedServicesData.services"
    />

    <FaqSection
      v-if="faqSectionData"
      :heading="faqSectionData.heading"
      :subheading="faqSectionData.subheading"
      :faqs="faqSectionData.faqs"
    />

    <OtherCitiesSection
      v-if="otherCitiesSectionData && otherCities.length"
      :heading="otherCitiesSectionData.heading"
      :subheading="otherCitiesSectionData.subheading"
      :cities="otherCities"
    />
  </div>
</template>

<script setup>
const route = useRoute()
const citySlug = route.params.id

// Fetch city-specific details
// Fetch all city details from the single JSON
const { data: allVoCityDetails, pending: voCityDetailsPending, error: voCityDetailsError } = await useAsyncData(
  'vo-city-details',
  () => $fetch('/api/vo-city-details')
)

// Find the current city's data from the array
const cityPageData = computed(() => {
  if (!allVoCityDetails.value || !Array.isArray(allVoCityDetails.value)) return null
  return allVoCityDetails.value.find(city => city.slug === citySlug)
})

// For the "Other Cities" section, we still need the list of all city slugs/names
const { data: allCitiesDataBasic } = await useFetch('/api/cities')


// Computed properties for each section from the found cityPageData
const heroData = computed(() => cityPageData.value?.hero)
const locationInfoData = computed(() => cityPageData.value?.locationInfo)
const pricingPlansData = computed(() => cityPageData.value?.pricingPlans)
const relatedServicesData = computed(() => cityPageData.value?.relatedServices)
const faqSectionData = computed(() => cityPageData.value?.faqSection)
const otherCitiesSectionData = computed(() => cityPageData.value?.otherCitiesSection) // This provides headings
const metaData = computed(() => cityPageData.value?.meta)

// Room images for the image slider - fully dynamic from admin dashboard
const roomImagesData = computed(() => {
  // Return actual data from admin dashboard or empty array if no images
  return cityPageData.value?.roomImages || [];
})

// For the "Other Cities" cards list
const otherCities = computed(() => {
  if (!allCitiesDataBasic.value?.cities || !cityPageData.value) return []
  return allCitiesDataBasic.value.cities.filter(c => c.id !== cityPageData.value.slug)
})

// Overall pending and error states for the template
const cityPagePending = voCityDetailsPending
const cityPageError = computed(() => voCityDetailsError.value || !cityPageData.value) // Error if fetch fails or city not found in array

// SEO Configuration
import { generateServiceSchema, generateWebPageSchema } from '~/utils/seo.js'

const config = useRuntimeConfig()
const baseUrl = config.public.siteUrl.replace(/\/$/, '') // Remove trailing slash

const pageSchemas = computed(() => {
  if (!cityPageData.value) return []

  const cityName = cityPageData.value.name || citySlug
  const pageUrl = `${baseUrl}/cities/${citySlug}`
  const pageTitle = cityPageData.value?.meta?.title || `Virtual Office in ${cityName} | Gemilang VO`
  const pageDescription = cityPageData.value?.meta?.description || `Professional virtual office services in ${cityName}. Get a prestigious business address and complete office support.`

  return [
    generateServiceSchema(
      `Virtual Office in ${cityName}`,
      pageDescription,
      baseUrl
    ),
    generateWebPageSchema(
      pageTitle,
      pageDescription,
      pageUrl,
      baseUrl
    )
  ]
})

useHead(() => {
  if (!cityPageData.value) return {}

  const cityName = cityPageData.value.name || citySlug
  const pageUrl = `${baseUrl}/cities/${citySlug}`
  const pageTitle = cityPageData.value?.meta?.title || `Virtual Office in ${cityName} | Gemilang VO`
  const pageDescription = cityPageData.value?.meta?.description || `Professional virtual office services in ${cityName}. Get a prestigious business address and complete office support.`

  return {
    title: pageTitle,
    meta: [
      { name: 'description', content: pageDescription },
      { name: 'keywords', content: `virtual office ${cityName.toLowerCase()}, business address ${cityName.toLowerCase()}, office rental ${cityName.toLowerCase()}, coworking space, meeting room` },
      { name: 'robots', content: 'index,follow' },
      { property: 'og:title', content: pageTitle },
      { property: 'og:description', content: pageDescription },
      { property: 'og:type', content: 'website' },
      { property: 'og:url', content: pageUrl },
      { property: 'og:site_name', content: 'Gemilang VO' },
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: pageTitle },
      { name: 'twitter:description', content: pageDescription }
    ],
    link: [
      { rel: 'canonical', href: pageUrl }
    ],
    script: [
      {
        type: 'application/ld+json',
        children: JSON.stringify(pageSchemas.value)
      }
    ]
  }
})

// No specific error logging here as the template handles cityPageError for "not found"
// The API endpoint itself logs server-side errors if voCities.json can't be read.
</script>
<template>
  <div>
    <!-- Blog Hero -->
    <div class="relative">
      <div class="absolute inset-0 z-0">
        <img 
          src="https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg" 
          alt="Blog" 
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-r from-primary-900/90 to-black/70"></div>
      </div>
      
      <div class="relative z-10 pt-32 pb-20">
        <div class="container mx-auto px-4">
          <div class="max-w-3xl animate-slide-up">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
              Blog & Artikel
            </h1>
            <p class="text-xl text-gray-200">
              Informasi dan tips seputar bisnis dan virtual office
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Blog Content -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <!-- Search Bar -->
        <div class="mb-6">
          <div class="relative max-w-md">
            <Icon name="heroicons:magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Cari artikel..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Categories -->
        <div class="flex flex-wrap gap-2 mb-8">
          <button
            v-for="category in categories"
            :key="category"
            @click="handleCategoryChange(category)"
            class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200"
            :class="[
              currentCategory === category
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
          >
            {{ category }}
          </button>
        </div>

        <!-- Loading State -->
        <div v-if="pending" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="i in 6" :key="i" class="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
            <div class="w-full h-48 bg-gray-200"></div>
            <div class="p-6">
              <div class="flex gap-2 mb-3">
                <div class="h-4 bg-gray-200 rounded w-16"></div>
                <div class="h-4 bg-gray-200 rounded w-20"></div>
              </div>
              <div class="h-6 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded mb-4"></div>
              <div class="flex justify-between">
                <div class="h-4 bg-gray-200 rounded w-24"></div>
                <div class="h-4 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Articles Grid -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <article v-for="article in articles" :key="article.id || article._path" class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <NuxtLink :to="`/blog/${article.slug || article._path?.replace('/blog/', '')}`">
              <img
                :src="article.featuredImage || article.image"
                :alt="article.title"
                class="w-full h-48 object-cover"
              />
              <div class="p-6">
                <div class="flex items-center gap-2 mb-3">
                  <span
                    v-for="tag in article.tags"
                    :key="tag"
                    class="text-xs font-medium px-2 py-1 rounded-full bg-primary-50 text-primary-700"
                  >
                    {{ typeof tag === 'string' ? tag : tag.name }}
                  </span>
                </div>
                <h2 class="text-xl font-bold mb-2 line-clamp-2 hover:text-primary-600 transition-colors duration-200">
                  {{ article.title }}
                </h2>
                <p class="text-gray-600 mb-4 line-clamp-3">
                  {{ article.description }}
                </p>
                <div class="flex items-center justify-between text-sm text-gray-500">
                  <span>{{ formatDate(article.publishedAt || article.date) }}</span>
                  <span class="flex items-center">
                    <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />
                    {{ article.readingTime }} min read
                  </span>
                </div>
              </div>
            </NuxtLink>
          </article>
        </div>

        <!-- No Results -->
        <div v-if="!pending && articles.length === 0" class="text-center py-12">
          <Icon name="heroicons:document-text" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 class="text-xl font-semibold text-gray-600 mb-2">Tidak ada artikel ditemukan</h3>
          <p class="text-gray-500">Coba ubah filter atau kata kunci pencarian Anda.</p>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="mt-12 flex justify-center gap-2">
          <button
            v-for="page in totalPages"
            :key="page"
            @click="handlePageChange(page)"
            class="w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200"
            :class="[
              currentPage === page
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
          >
            {{ page }}
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// Reactive state
const currentCategory = ref('All')
const currentPage = ref(1)
const searchQuery = ref('')

// Computed query parameters
const queryParams = computed(() => ({
  page: currentPage.value,
  limit: 9,
  category: currentCategory.value !== 'All' ? currentCategory.value : undefined,
  search: searchQuery.value || undefined
}))

// Fetch blog data with reactivity
const { data: blogData, pending } = await useAsyncData(
  'blog-posts',
  () => $fetch('/api/blog', { query: queryParams.value }),
  {
    watch: [queryParams]
  }
)

// Computed properties
const articles = computed(() => blogData.value?.data || [])
const categories = computed(() => {
  const cats = blogData.value?.categories || []
  return ['All', ...cats.map(cat => cat.name)]
})
const totalPages = computed(() => blogData.value?.meta?.pagination?.pageCount || 1)

// Methods
const formatDate = (date) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const handleCategoryChange = (category) => {
  currentCategory.value = category
  currentPage.value = 1 // Reset to first page when changing category
}

const handlePageChange = (page) => {
  currentPage.value = page
}

// SEO with programmatic schema
const blogSchema = computed(() => {
  if (!articles.value) return null
  return generateBlogSchema(articles.value)
})

const organizationSchema = computed(() => {
  return generateOrganizationSchema()
})

// SEO Meta
useHead(() => ({
  title: 'Blog & Artikel | Gemilang VO',
  meta: [
    { name: 'description', content: 'Baca artikel dan tips terbaru seputar bisnis, virtual office, dan perkembangan dunia usaha di Indonesia.' },
    { name: 'keywords', content: 'blog bisnis, tips virtual office, artikel startup, panduan bisnis indonesia' },
    { property: 'og:type', content: 'website' },
    { property: 'og:title', content: 'Blog & Artikel | Gemilang VO' },
    { property: 'og:description', content: 'Baca artikel dan tips terbaru seputar bisnis, virtual office, dan perkembangan dunia usaha di Indonesia.' },
    { property: 'og:url', content: `${process.env.NUXT_PUBLIC_SITE_URL || 'https://sewavirtualoffice.id/'}/blog` },
    { name: 'twitter:card', content: 'summary_large_image' }
  ],
  link: [
    { rel: 'canonical', href: `${process.env.NUXT_PUBLIC_SITE_URL || 'https://sewavirtualoffice.id/'}/blog` }
  ],
  script: [
    // Blog Schema
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(blogSchema.value)
    },
    // Organization Schema
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(organizationSchema.value)
    }
  ]
}))
</script>
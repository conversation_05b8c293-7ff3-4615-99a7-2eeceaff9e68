// Import the JSON file directly
import reportData from '~/data/laporan.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Financial report data loaded successfully');
    return reportData;
  } catch (error) {
    console.error('Error loading financial report data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load financial report data',
    });
  }
});
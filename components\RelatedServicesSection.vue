<template>
  <section v-if="heading && services && services.length" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <SectionHeading 
        :title="heading"
        :subtitle="subheading"
      />
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
        <ServiceItemCard v-for="service in services" :key="service.id" :service="service" />
      </div>
    </div>
  </section>
</template>

<script setup>
import SectionHeading from '~/components/atoms/SectionHeading.vue' // Assuming this path is correct
import ServiceItemCard from '~/components/ServiceItemCard.vue'

defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  services: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>
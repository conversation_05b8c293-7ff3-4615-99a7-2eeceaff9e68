<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test WhatsApp Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .whatsapp-link {
            display: inline-block;
            background: #25D366;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .whatsapp-link:hover {
            background: #128C7E;
        }
        .message-preview {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>Test WhatsApp Functionality - PricingPlansSection</h1>
    
    <div class="test-section">
        <h2>Test Case 1: <PERSON><PERSON> - Basic Plan</h2>
        <div class="message-preview">
            Message: "Hal<PERSON>, saya tertarik dengan paket Basic (Rp 1.5 Juta) untuk Jasa Laporan Keuangan. Bisakah saya mendapatkan informasi lebih detail dan proses pendaftarannya?"
        </div>
        <a href="https://wa.me/6287799088880?text=Halo%2C%20saya%20tertarik%20dengan%20paket%20Basic%20(Rp%201.5%20Juta)%20untuk%20Jasa%20Laporan%20Keuangan.%20Bisakah%20saya%20mendapatkan%20informasi%20lebih%20detail%20dan%20proses%20pendaftarannya%3F" 
           class="whatsapp-link" target="_blank">
            📱 Test WhatsApp Link - Basic Plan
        </a>
    </div>

    <div class="test-section">
        <h2>Test Case 2: Sewa Ruang Meeting - Half Day Plan</h2>
        <div class="message-preview">
            Message: "Halo, saya tertarik dengan paket Paket Half Day (Rp 500.000) untuk Sewa Ruang Meeting. Bisakah saya mendapatkan informasi lebih detail dan proses pendaftarannya?"
        </div>
        <a href="https://wa.me/6287799088880?text=Halo%2C%20saya%20tertarik%20dengan%20paket%20Paket%20Half%20Day%20(Rp%20500.000)%20untuk%20Sewa%20Ruang%20Meeting.%20Bisakah%20saya%20mendapatkan%20informasi%20lebih%20detail%20dan%20proses%20pendaftarannya%3F" 
           class="whatsapp-link" target="_blank">
            📱 Test WhatsApp Link - Half Day Plan
        </a>
    </div>

    <div class="test-section">
        <h2>Test Case 3: Jasa Perizinan - Professional Plan</h2>
        <div class="message-preview">
            Message: "Halo, saya tertarik dengan paket Professional (Rp 5 Juta) untuk Jasa Perizinan. Bisakah saya mendapatkan informasi lebih detail dan proses pendaftarannya?"
        </div>
        <a href="https://wa.me/6287799088880?text=Halo%2C%20saya%20tertarik%20dengan%20paket%20Professional%20(Rp%205%20Juta)%20untuk%20Jasa%20Perizinan.%20Bisakah%20saya%20mendapatkan%20informasi%20lebih%20detail%20dan%20proses%20pendaftarannya%3F" 
           class="whatsapp-link" target="_blank">
            📱 Test WhatsApp Link - Professional Plan
        </a>
    </div>

    <div class="test-section">
        <h2>Verification Steps:</h2>
        <ol>
            <li>Click on each WhatsApp link above</li>
            <li>Verify that WhatsApp Web/App opens</li>
            <li>Check that the phone number is +6287799088880</li>
            <li>Verify that the message is pre-filled correctly</li>
            <li>Confirm that the message includes:
                <ul>
                    <li>Plan name</li>
                    <li>Plan price</li>
                    <li>Service name</li>
                    <li>Request for more information</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Expected Behavior:</h2>
        <ul>
            <li>✅ WhatsApp opens with correct phone number (+6287799088880)</li>
            <li>✅ Message is pre-filled with plan details</li>
            <li>✅ Message includes service name</li>
            <li>✅ Message is properly URL encoded</li>
            <li>✅ User can send message directly</li>
        </ul>
    </div>

    <script>
        // JavaScript to generate WhatsApp links dynamically (similar to Vue component)
        function generateWhatsAppLink(planName, price, serviceName, phoneNumber = '6287799088880') {
            const message = `Halo, saya tertarik dengan paket ${planName} ${price ? `(${price})` : ''} untuk ${serviceName}. Bisakah saya mendapatkan informasi lebih detail dan proses pendaftarannya?`;
            return `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
        }

        // Test the function
        console.log('Generated WhatsApp Link:', generateWhatsAppLink('Basic', 'Rp 1.5 Juta', 'Jasa Laporan Keuangan'));
    </script>
</body>
</html>

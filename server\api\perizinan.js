// Import the JSON file directly
import perizinanData from '~/data/perizinan.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('Perizinan data loaded successfully');
    return perizinanData;
  } catch (error) {
    console.error('Error loading perizinan data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load perizinan data',
    });
  }
});
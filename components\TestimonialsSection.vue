<template>
  <section v-if="heading && testimonials.length" class="py-20 bg-primary-900 text-white" aria-labelledby="testimonials-heading">
    <div class="container mx-auto px-4">
      <h2 id="testimonials-heading" class="text-3xl md:text-4xl font-bold text-center text-white mb-4">
        {{ heading }}
      </h2>
      <p v-if="subheading" class="text-primary-100 text-center max-w-2xl mx-auto mb-12">
        {{ subheading }}
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <TestimonialCard v-for="(testimonial, index) in testimonials" :key="index" :testimonial="testimonial" />
      </div>
    </div>
  </section>
</template>

<script setup>
import TestimonialCard from '~/components/TestimonialCard.vue'

defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  testimonials: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>
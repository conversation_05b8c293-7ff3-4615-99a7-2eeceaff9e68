<template>
  <header class="fixed top-0 w-full z-40 bg-zinc-100 backdrop-blur-sm shadow-sm">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between h-20">
        <NuxtLink to="/" class="flex items-center">
          <!-- <span class="text-2xl font-bold text-primary-600">Gemilang VO</span> -->
          <img src="/Gemilang-Logo.webp" alt="Gemilang VO" class="h-20" />
        </NuxtLink>

        <!-- Desktop Menu -->
        <nav class="hidden md:flex space-x-8">
          <div v-for="item in navItems" :key="item.path" 
            class="relative group"
            @mouseenter="item.children && (activeDropdown = item.path)"
            @mouseleave="handleMouseLeave"
          >
            <NuxtLink 
              :to="item.path"
              class="font-medium transition-colors duration-200 py-2 text-gray-700 hover:text-primary-600" 
            >
              <span class="flex items-center">
                {{ item.name }}
                <Icon 
                  v-if="item.children"
                  name="heroicons:chevron-down"
                  class="ml-1 w-4 h-4 transition-transform duration-200"
                  :class="{'rotate-180': activeDropdown === item.path}"
                />
              </span>
            </NuxtLink>
            
            <!-- Dropdown Menu -->
            <div v-if="item.children" 
              class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg py-2 transition-all duration-200 origin-top"
              :class="[
                activeDropdown === item.path ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none',
                'transform'
              ]"
              @mouseenter="clearCloseTimeout"
              @mouseleave="handleMouseLeave"
            >
              <NuxtLink 
                v-for="child in item.children" 
                :key="child.path" 
                :to="child.path"
                class="block px-4 py-3 text-gray-800 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-150"
              >
                {{ child.name }}
              </NuxtLink>
            </div>
          </div>
        </nav>

        <div class="hidden md:block">
          <AppButton>Contact Us</AppButton>
        </div>

        <!-- Mobile Menu Button -->
        <button @click="toggleMobileMenu" class="md:hidden text-gray-800">
          <Icon v-if="!mobileMenuOpen" name="heroicons:bars-3" size="24" />
          <Icon v-else name="heroicons:x-mark" size="24" />
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div v-if="mobileMenuOpen" class="md:hidden bg-white shadow-md">
      <div class="container mx-auto px-4 py-4">
        <div class="space-y-4">
          <div v-for="item in navItems" :key="item.path">
            <!-- Main Menu Item -->
            <NuxtLink 
              v-if="!item.children"
              :to="item.path" 
              class="block font-medium text-gray-800 hover:text-primary-600 py-2"
              @click="mobileMenuOpen = false"
            >
              {{ item.name }}
            </NuxtLink>
            
            <!-- Menu Item with Children -->
            <div v-else class="space-y-2">
              <button 
                class="flex items-center justify-between w-full font-medium text-gray-800 hover:text-primary-600 py-2"
                @click="toggleMobileSubmenu(item.path)"
              >
                {{ item.name }}
                <Icon 
                  :name="mobileActiveSubmenu === item.path ? 'heroicons:chevron-up' : 'heroicons:chevron-down'"
                  class="w-4 h-4 transition-transform duration-200"
                />
              </button>
              <div 
                v-show="mobileActiveSubmenu === item.path"
                class="pl-4 space-y-2"
              >
                <NuxtLink 
                  v-for="child in item.children"
                  :key="child.path"
                  :to="child.path"
                  class="block text-gray-600 hover:text-primary-600 py-2"
                  @click="mobileMenuOpen = false"
                >
                  {{ child.name }}
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
        <AppButton class="w-full mt-4">Contact Us</AppButton>
      </div>
    </div>
  </header>
</template>

<script setup>
const navItems = [
  { name: 'Home', path: '/' },
  { name: 'Cities', path: '/cities' },
  { 
    name: 'Layanan', 
    path: '/services',
    children: [
      { name: 'Jasa Pengurusan Perizinan', path: '/jasa-perizinan' },
      { name: 'Jasa Pembuatan PT/CV', path: '/jasa-pembuatan-pt-cv' },
      { name: 'Sewa Ruang Meeting', path: '/sewa-ruang-meeting' },
      { name: 'Asisten Virtual', path: '/asisten-virtual' },
      { name: 'Jasa Laporan Keuangan', path: '/jasa-laporan-keuangan' },
      { name: 'Jasa Perpajakan', path: '/jasa-perpajakan' },
      { name: 'Jasa Pendaftaran Logo', path: '/jasa-pendaftaran-logo' }
    ]
  },
  { name: 'Blog', path: '/blog' },
  { name: 'About', path: '/about' }
]

const mobileMenuOpen = ref(false)
const activeDropdown = ref(null)
const mobileActiveSubmenu = ref(null)
const closeTimeout = ref(null)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
  mobileActiveSubmenu.value = null
}

const toggleMobileSubmenu = (path) => {
  mobileActiveSubmenu.value = mobileActiveSubmenu.value === path ? null : path
}

const handleMouseLeave = () => {
  closeTimeout.value = setTimeout(() => {
    activeDropdown.value = null
  }, 150)
}

const clearCloseTimeout = () => {
  if (closeTimeout.value) {
    clearTimeout(closeTimeout.value)
    closeTimeout.value = null
  }
}

onMounted(() => {
  if (closeTimeout.value) {
    clearTimeout(closeTimeout.value)
  }
})
</script>
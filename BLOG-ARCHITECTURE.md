# Blog Architecture Documentation

## 📋 Overview
Blog system telah dimigrasi dari **Nuxt Content** ke **JSON-based data structure** yang konsisten dengan arsitektur aplikasi lainnya. Sistem ini siap untuk migrasi ke Strapi CMS di masa depan.

## 🏗️ Architecture Pattern

### Current Structure (Development)
```
data/blog.json          → Local JSON data (development)
server/api/blog.js      → Blog listing API endpoint
server/api/blog/[slug].js → Individual blog post API endpoint
pages/blog/index.vue    → Blog listing page
pages/blog/[...slug].vue → Individual blog post page
```

### Future Structure (Production with Strapi)
```
Strapi CMS              → Content management via admin dashboard
server/api/blog.js      → Same API endpoints (auto-switch to Strapi)
server/api/blog/[slug].js → Same API endpoints (auto-switch to Strapi)
pages/blog/index.vue    → Same pages (no changes needed)
pages/blog/[...slug].vue → Same pages (no changes needed)
```

## 📊 Data Structure

### Blog Post Object
```json
{
  "id": 1,
  "title": "Article Title",
  "slug": "article-slug",
  "description": "Article description",
  "content": "<h1>HTML Content</h1><p>Article content...</p>",
  "featuredImage": "https://example.com/image.jpg",
  "publishedAt": "2024-04-03T10:00:00.000Z",
  "readingTime": 8,
  "category": "Business Tips",
  "tags": ["Startup", "Business Planning"],
  "author": {
    "name": "Author Name",
    "bio": "Author bio",
    "avatar": "https://example.com/avatar.jpg"
  },
  "seo": {
    "metaTitle": "SEO Title",
    "metaDescription": "SEO Description",
    "keywords": "keyword1, keyword2"
  }
}
```

### Categories Structure
```json
{
  "id": 1,
  "name": "Business Tips",
  "slug": "business-tips",
  "description": "Tips dan strategi untuk mengembangkan bisnis",
  "color": "#3B82F6"
}
```

### Tags Structure
```json
{
  "id": 1,
  "name": "Startup",
  "slug": "startup"
}
```

## 🔄 API Endpoints

### GET /api/blog
**Parameters:**
- `page` (number): Page number for pagination
- `limit` (number): Items per page
- `category` (string): Filter by category name
- `tag` (string): Filter by tag name
- `search` (string): Search in title, description, content

**Response:**
```json
{
  "data": [...], // Array of blog posts
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 9,
      "pageCount": 5,
      "total": 42
    }
  },
  "categories": [...], // Available categories
  "tags": [...] // Available tags
}
```

### GET /api/blog/[slug]
**Response:**
```json
{
  "data": {...}, // Single blog post object
  "relatedPosts": [...] // Array of related posts (same category)
}
```

## 🎯 Features

### Blog Listing Page (`/blog`)
- ✅ Search functionality
- ✅ Category filtering
- ✅ Pagination
- ✅ Responsive grid layout
- ✅ Loading states
- ✅ Empty states
- ✅ SEO optimization

### Individual Blog Post (`/blog/[slug]`)
- ✅ Rich HTML content rendering
- ✅ Featured image display
- ✅ Author information
- ✅ Reading time estimation
- ✅ Tags and categories
- ✅ Social sharing buttons
- ✅ Related articles
- ✅ SEO meta tags

## 🚀 Environment Configuration

### Development Mode
```bash
NODE_ENV=development
# Uses local JSON data from data/blog.json
```

### Production Mode (Future with Strapi)
```bash
NODE_ENV=production
STRAPI_URL=https://your-strapi-instance.com
# Uses Strapi API endpoints
```

## 📝 Content Management

### Current (Development)
- Edit `data/blog.json` file directly
- Add new posts to the `posts` array
- Manage categories and tags in respective arrays

### Future (Production with Strapi)
- Use Strapi admin dashboard
- Rich text editor for content
- Media library for images
- User-friendly content management
- Content scheduling and publishing workflow

## 🔧 Migration Benefits

### For Developers
- ✅ Consistent API structure across all pages
- ✅ Same development patterns
- ✅ Easy to maintain and extend
- ✅ Ready for Strapi migration

### For Content Managers
- ✅ No need to learn Markdown
- ✅ WYSIWYG editor (with Strapi)
- ✅ Easy image management
- ✅ Content scheduling
- ✅ User-friendly admin interface

### For Performance
- ✅ API-based data fetching
- ✅ Built-in caching support
- ✅ Optimized for SSR/SSG
- ✅ Better SEO control

## 📋 Next Steps for Strapi Migration

1. **Setup Strapi Instance**
   - Install and configure Strapi
   - Create content types (Blog Post, Category, Tag, Author)
   - Configure media library

2. **Data Migration**
   - Create migration script to transfer existing data
   - Upload images to Strapi media library
   - Test API endpoints

3. **Environment Switch**
   - Update environment variables
   - Test production deployment
   - Monitor performance

## 🎉 Conclusion

Blog system sekarang menggunakan arsitektur yang konsisten dengan halaman lain dalam aplikasi:
- **Data Source**: JSON files (development) → Strapi CMS (production)
- **API Pattern**: Server API endpoints dengan auto-switching
- **Frontend**: Vue components yang sama untuk kedua environment
- **Content Management**: File-based (development) → Admin dashboard (production)

Sistem ini memberikan fleksibilitas untuk development yang cepat sambil mempersiapkan migrasi ke CMS yang lebih powerful untuk production.

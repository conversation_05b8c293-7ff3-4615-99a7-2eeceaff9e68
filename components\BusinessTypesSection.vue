<template>
  <section id="business-types" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          {{ heading }}
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          {{ subheading }}
        </p>
      </div>

      <!-- Business Types Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
        <div 
          v-for="(type, index) in types" 
          :key="index"
          class="bg-white rounded-xl shadow-lg p-8 transition-all duration-300 hover:shadow-xl"
        >
          <!-- Header with Icon -->
          <div class="flex items-center mb-4">
            <div class="bg-primary-100 p-3 rounded-lg mr-4">
              <Icon :name="type.icon" class="text-primary-600 w-8 h-8" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900">{{ type.name }}</h3>
          </div>
          
          <!-- Description -->
          <p class="text-gray-600 mb-6">{{ type.description }}</p>
          
          <!-- Features List -->
          <ul class="space-y-3">
            <li 
              v-for="(feature, featureIndex) in type.features" 
              :key="featureIndex"
              class="flex items-start"
            >
              <Icon name="heroicons:check-circle" class="text-primary-600 w-5 h-5 mt-1 mr-2 flex-shrink-0" />
              <span class="text-gray-700">{{ feature }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({
  heading: {
    type: String,
    default: 'Pilih Jenis Perusahaan'
  },
  subheading: {
    type: String,
    default: 'Tentukan badan usaha yang sesuai dengan kebutuhan bisnis Anda'
  },
  types: {
    type: Array,
    default: () => []
  }
})
</script>
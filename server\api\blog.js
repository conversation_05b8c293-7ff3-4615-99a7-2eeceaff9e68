// server/api/blog.js
// API endpoint untuk blog posts dengan Strapi integration

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { 
      page = 1, 
      limit = 9, 
      category, 
      tag, 
      search,
      populate = 'deep'
    } = query

    // Untuk development - gunakan data lokal
    if (process.env.NODE_ENV === 'development') {
      // Import data lokal untuk development
      const { default: blogData } = await import('~/data/blog.json')
      
      let filteredPosts = blogData.posts || []
      
      // Filter by category
      if (category && category !== 'All') {
        filteredPosts = filteredPosts.filter(post => 
          post.category === category
        )
      }
      
      // Filter by tag
      if (tag) {
        filteredPosts = filteredPosts.filter(post => 
          post.tags.includes(tag)
        )
      }
      
      // Search functionality
      if (search) {
        const searchLower = search.toLowerCase()
        filteredPosts = filteredPosts.filter(post => 
          post.title.toLowerCase().includes(searchLower) ||
          post.description.toLowerCase().includes(searchLower) ||
          post.content.toLowerCase().includes(searchLower)
        )
      }
      
      // Sort by date (newest first)
      filteredPosts.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
      
      // Pagination
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + parseInt(limit)
      const paginatedPosts = filteredPosts.slice(startIndex, endIndex)
      
      return {
        data: paginatedPosts,
        meta: {
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(limit),
            pageCount: Math.ceil(filteredPosts.length / limit),
            total: filteredPosts.length
          }
        },
        categories: blogData.categories || [],
        tags: blogData.tags || []
      }
    }

    // Production - gunakan Strapi API
    const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1337'
    
    // Build query parameters untuk Strapi
    const strapiParams = new URLSearchParams({
      'pagination[page]': page,
      'pagination[pageSize]': limit,
      'populate': populate,
      'sort': 'publishedAt:desc'
    })
    
    // Add filters
    if (category && category !== 'All') {
      strapiParams.append('filters[category][slug][$eq]', category)
    }
    
    if (tag) {
      strapiParams.append('filters[tags][slug][$eq]', tag)
    }
    
    if (search) {
      strapiParams.append('filters[$or][0][title][$containsi]', search)
      strapiParams.append('filters[$or][1][description][$containsi]', search)
    }
    
    // Fetch from Strapi
    const response = await $fetch(`${STRAPI_URL}/api/blog-posts?${strapiParams}`)
    
    // Fetch categories and tags for filters
    const [categoriesResponse, tagsResponse] = await Promise.all([
      $fetch(`${STRAPI_URL}/api/categories`),
      $fetch(`${STRAPI_URL}/api/tags`)
    ])
    
    return {
      data: response.data,
      meta: response.meta,
      categories: categoriesResponse.data,
      tags: tagsResponse.data
    }
    
  } catch (error) {
    console.error('Blog API Error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch blog posts'
    })
  }
})

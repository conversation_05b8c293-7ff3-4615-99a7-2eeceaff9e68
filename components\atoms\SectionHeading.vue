<template>
  <div class="text-center mb-12">
    <h2 :class="`text-3xl md:text-4xl font-bold mb-4 ${textColorClass}`">
      {{ title }}
    </h2>
    <p v-if="subtitle" class="text-gray-600 max-w-2xl mx-auto">
      {{ subtitle }}
    </p>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  light: {
    type: Boolean,
    default: false
  }
})

const textColorClass = computed(() => props.light ? 'text-white' : 'text-gray-900')
</script>
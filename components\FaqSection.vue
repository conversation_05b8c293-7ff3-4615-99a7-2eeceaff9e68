<template>
  <section v-if="heading && faqs.length" class="py-16 bg-gray-50" aria-labelledby="faq-heading">
    <div class="container mx-auto px-4 max-w-3xl">
      <h2 id="faq-heading" class="text-3xl md:text-4xl font-bold text-center mb-4">
        {{ heading }}
      </h2>
      <p v-if="subheading" class="text-gray-600 text-center max-w-2xl mx-auto mb-12">
        {{ subheading }}
      </p>
      
      <div class="space-y-4">
        <FaqItem 
          v-for="(faq, index) in faqs" 
          :key="index" 
          :faq="faq"
          :is-active="activeFaq === index"
          @toggle="toggleFaq(index)"
        />
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'
import FaqItem from '~/components/FaqItem.vue'

defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  faqs: {
    type: Array,
    required: true,
    default: () => []
  }
})

const activeFaq = ref(null)

const toggleFaq = (index) => {
  activeFaq.value = activeFaq.value === index ? null : index
}
</script>
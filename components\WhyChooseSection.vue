<template>
  <section v-if="heading && features" class="py-20" aria-labelledby="why-choose-heading">
    <div class="container mx-auto px-4">
      <h2 id="why-choose-heading" class="text-3xl md:text-4xl font-bold text-center mb-4">
        {{ heading }}
      </h2>
      <p v-if="subheading" class="text-gray-600 text-center max-w-2xl mx-auto mb-12">
        {{ subheading }}
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
       <div
  v-for="(feature, index) in features"
  :key="index"
  class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex items-start gap-6"
>
  <!-- Icon with border -->
  <div class="min-w-[64px] min-h-[64px] bg-primary-100 rounded-xl flex items-center justify-center">
  <Icon :name="feature.icon" size="32" class="text-primary-600" />
</div>

  <!-- Text -->
  <div>
    <h3 class="text-xl font-semibold mb-1">{{ feature.title }}</h3>
    <p class="text-gray-600 text-sm leading-relaxed">{{ feature.description }}</p>
  </div>
</div>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  features: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>
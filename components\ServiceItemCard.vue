<template>
  <NuxtLink 
    :to="service.path"
    class="bg-white p-6 rounded-2xl border-2 border-purple-300 shadow-sm hover:shadow-lg transition-all duration-300 group space-y-6 "
  >
    <div class="w-12 h-12 bg-primary-400 rounded-full flex items-center justify-center text-primary-100 mb-4 group-hover:bg-primary-600 group-hover:text-white transition-colors duration-300">
      <Icon :name="service.icon" size="24" />
    </div>
    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 transition-colors duration-300">
      {{ service.name }}
    </h3>
    <p class="text-gray-600 mb-4">{{ service.description }}</p>
    <div class="flex items-center text-primary-600 font-medium">
      <span>Pelajar<PERSON> Lebih <PERSON>n<PERSON>t</span>
      <Icon name="heroicons:arrow-right" class="ml-2 w-5 h-5 group-hover:translate-x-2 transition-transform duration-300" />
    </div>
  </NuxtLink>
</template>

<script setup>
defineProps({
  service: {
    type: Object,
    required: true
  }
})
</script>
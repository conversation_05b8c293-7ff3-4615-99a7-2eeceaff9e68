<template>
  <section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="max-w-3xl mx-auto text-center mb-16">
        <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
          {{ badgeText }}
        </span>
        <h2 class="text-3xl md:text-4xl font-bold mb-6">{{ heading }}</h2>
        <p class="text-gray-600 text-lg">
          {{ subheading }}
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div v-for="(step, index) in steps" :key="index" class="relative">
          <div class="bg-white rounded-2xl p-8 shadow-sm h-full">
            <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold mb-6">
              {{ index + 1 }}
            </div>
            <h3 class="text-xl font-bold mb-4">{{ step.title }}</h3>
            <p class="text-gray-600">{{ step.description }}</p>
          </div>
          <!-- Connector Line -->
          <div v-if="index < steps.length - 1" class="hidden md:block absolute top-1/2 -right-4 w-8 border-t-2 border-dashed border-primary-300"></div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({
  badgeText: {
    type: String,
    default: 'Proses Kerja'
  },
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  steps: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>
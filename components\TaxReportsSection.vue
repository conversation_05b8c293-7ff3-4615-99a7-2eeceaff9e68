<template>
  <section class="py-20" :class="bgClass">
    <div class="container mx-auto px-4">
      <div class="max-w-3xl mx-auto text-center mb-16">
        <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
          {{ badgeText }}
        </span>
        <h2 class="text-3xl md:text-4xl font-bold mb-6">{{ heading }}</h2>
        <p class="text-gray-600 text-lg">
          {{ subheading }}
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div v-for="(feature, index) in features" :key="index" 
            class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
          <div class="flex items-start mb-6">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
              <Icon :name="feature.icon" class="w-6 h-6 text-primary-600" />
            </div>
            <div>
              <h3 class="text-xl font-bold mb-2">{{ feature.title }}</h3>
              <p class="text-gray-600">{{ feature.description }}</p>
            </div>
          </div>
          <ul class="space-y-3">
            <li v-for="item in feature.items" :key="item" class="flex items-start">
              <Icon name="heroicons:check-circle" class="text-primary-500 w-5 h-5 mr-2 mt-1" />
              <span class="text-gray-700">{{ item }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({
  badgeText: {
    type: String,
    default: 'Kepatuhan Pajak'
  },
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  features: {
    type: Array,
    required: true,
    default: () => []
  },
  bgClass: {
    type: String,
    default: 'bg-gray-50'
  }
})
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm">
    <button
      @click="$emit('toggle')"
      class="w-full flex justify-between items-center p-6 text-left"
    >
      <h3 class="text-lg font-medium">{{ faq.question }}</h3>
      <Icon
        :name="isActive ? 'heroicons:chevron-up' : 'heroicons:chevron-down'"
        class="text-primary-600 flex-shrink-0 ml-4"
      />
    </button>
    <div v-show="isActive" class="px-6 pb-6">
      <p class="text-gray-600">{{ faq.answer }}</p>
    </div>
  </div>
</template>

<script setup>
defineProps({
  faq: {
    type: Object,
    required: true
  },
  isActive: {
    type: Boolean,
    default: false
  }
})
defineEmits(['toggle'])
</script>
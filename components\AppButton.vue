<template>
  <a 
    v-if="href"
    :href="href"
    target="_blank"
    rel="noopener noreferrer"
    :class="[
      'font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-lg inline-flex items-center justify-center',
      sizeClasses,
      variantClasses,
      disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
      className
    ]"
  >
    <Icon v-if="whatsapp" name="mdi:whatsapp" class="mr-2 w-5 h-5" />
    <slot />
  </a>
  <button 
    v-else
    :class="[
      'font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-lg inline-flex items-center justify-center',
      sizeClasses,
      variantClasses,
      disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
      className
    ]"
    :disabled="disabled"
    @click="$emit('click')"
  >
    <Icon v-if="whatsapp" name="mdi:whatsapp" class="mr-2 w-5 h-5" />
    <slot />
  </button>
</template>

<script setup>
const props = defineProps({
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'outline', 'ghost'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  className: {
    type: String,
    default: ''
  },
  href: {
    type: String,
    default: ''
  },
  whatsapp: {
    type: Boolean,
    default: false
  }
})

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm': return 'px-3 py-1.5 text-sm'
    case 'lg': return 'px-6 py-3 text-lg'
    default: return 'px-4 py-2 text-base'
  }
})

const variantClasses = computed(() => {
  switch (props.variant) {
    case 'primary': return 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'
    case 'secondary': return 'bg-secondary-600 hover:bg-secondary-700 text-white focus:ring-secondary-500'
    case 'outline': return 'bg-transparent border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500'
    case 'ghost': return 'bg-transparent text-primary-600 hover:bg-primary-50 focus:ring-primary-500'
    default: return 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'
  }
})
</script>
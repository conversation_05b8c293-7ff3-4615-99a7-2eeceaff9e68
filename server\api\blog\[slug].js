// server/api/blog/[slug].js
// API endpoint untuk individual blog post dengan Strapi integration

export default defineEventHandler(async (event) => {
  try {
    const slug = getRouterParam(event, 'slug')
    
    if (!slug) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Slug parameter is required'
      })
    }

    // Untuk development - gunakan data lokal
    if (process.env.NODE_ENV === 'development') {
      const { default: blogData } = await import('~/data/blog.json')

      const post = blogData.posts?.find(p => p.slug === slug)
      
      if (!post) {
        throw createError({
          statusCode: 404,
          statusMessage: 'Blog post not found'
        })
      }
      
      // Get related posts (same category, exclude current post)
      const relatedPosts = blogData.posts
        ?.filter(p => p.slug !== slug && p.category === post.category)
        ?.slice(0, 3) || []
      
      return {
        data: post,
        relatedPosts
      }
    }

    // Production - gunakan Strapi API
    const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1337'
    
    // Fetch blog post by slug
    const response = await $fetch(`${STRAPI_URL}/api/blog-posts`, {
      query: {
        'filters[slug][$eq]': slug,
        'populate': 'deep'
      }
    })
    
    if (!response.data || response.data.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Blog post not found'
      })
    }
    
    const post = response.data[0]
    
    // Fetch related posts (same category, exclude current post)
    const relatedResponse = await $fetch(`${STRAPI_URL}/api/blog-posts`, {
      query: {
        'filters[category][id][$eq]': post.attributes.category.data.id,
        'filters[id][$ne]': post.id,
        'pagination[limit]': 3,
        'populate': 'deep'
      }
    })
    
    return {
      data: post,
      relatedPosts: relatedResponse.data || []
    }
    
  } catch (error) {
    console.error('Blog Post API Error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch blog post'
    })
  }
})

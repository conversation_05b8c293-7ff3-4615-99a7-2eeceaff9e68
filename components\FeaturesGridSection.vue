<template>
  <section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <div class="inline-block px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-semibold mb-4">
          {{ badgeText }}
        </div>
        <h2 class="text-4xl font-bold mb-4">{{ heading }}</h2>
        <p class="text-xl text-gray-600">{{ subheading }}</p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div 
          v-for="(feature, index) in features" 
          :key="index"
          class="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300"
        >
          <div class="bg-primary-100 w-14 h-14 rounded-lg flex items-center justify-center mb-6">
            <Icon :name="feature.icon" class="text-primary-600 w-7 h-7" />
          </div>
          <h3 class="text-xl font-bold mb-3">{{ feature.title }}</h3>
          <p class="text-gray-600">{{ feature.description }}</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({
  badgeText: {
    type: String,
    default: 'Fitur Unggulan'
  },
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  features: {
    type: Array,
    required: true
  }
})
</script>
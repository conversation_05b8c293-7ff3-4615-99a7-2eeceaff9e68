// utils/seo.js
// SEO utility functions untuk generate canonical URL dan schema markup

/**
 * Generate canonical URL untuk blog post
 * @param {string} slug - Blog post slug
 * @param {string} customCanonical - Custom canonical URL (optional)
 * @returns {string} Canonical URL
 */
export function generateCanonicalUrl(slug, customCanonical = null) {
  if (customCanonical) {
    return customCanonical
  }

  const config = useRuntimeConfig()
  const baseUrl = config.public.siteUrl
  return `${baseUrl}/blog/${slug}`
}

/**
 * Generate Article Schema Markup (JSON-LD)
 * @param {Object} article - Blog post object
 * @param {string} canonicalUrl - Canonical URL
 * @returns {Object} Schema markup object
 */
export function generateArticleSchema(article, canonicalUrl) {
  const config = useRuntimeConfig()
  const baseUrl = config.public.siteUrl
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    image: {
      '@type': 'ImageObject',
      url: article.featuredImage,
      width: 1200,
      height: 630
    },
    author: {
      '@type': 'Person',
      name: article.author?.name || 'Gemilang VO Team',
      description: article.author?.bio || 'Tim ahli virtual office dan konsultan bisnis'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Gemilang VO',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/logo.png`,
        width: 200,
        height: 60
      }
    },
    datePublished: article.publishedAt,
    dateModified: article.updatedAt || article.publishedAt,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': canonicalUrl
    },
    url: canonicalUrl,
    keywords: article.seo?.keywords || article.tags?.join(', '),
    articleSection: article.category,
    wordCount: estimateWordCount(article.content),
    timeRequired: `PT${article.readingTime}M`,
    inLanguage: 'id-ID',
    isAccessibleForFree: true
  }
}

/**
 * Generate BreadcrumbList Schema
 * @param {Object} article - Blog post object
 * @returns {Object} Breadcrumb schema
 */
export function generateBreadcrumbSchema(article) {
  const baseUrl = process.env.NUXT_PUBLIC_SITE_URL || 'https://sewavirtualoffice.id/'
  
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: baseUrl
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'Blog',
        item: `${baseUrl}/blog`
      },
      {
        '@type': 'ListItem',
        position: 3,
        name: article.category,
        item: `${baseUrl}/blog?category=${encodeURIComponent(article.category)}`
      },
      {
        '@type': 'ListItem',
        position: 4,
        name: article.title,
        item: `${baseUrl}/blog/${article.slug}`
      }
    ]
  }
}

/**
 * Generate Organization Schema untuk blog listing
 * @returns {Object} Organization schema
 */
export function generateOrganizationSchema() {
  const baseUrl = process.env.NUXT_PUBLIC_SITE_URL || 'https://sewavirtualoffice.id/'
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Gemilang VO',
    description: 'Penyedia layanan virtual office terpercaya di Indonesia',
    url: baseUrl,
    logo: `${baseUrl}/logo.png`,
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+62-877-9908-8880',
      contactType: 'customer service',
      availableLanguage: ['Indonesian', 'English']
    },
    sameAs: [
      'https://facebook.com/gemilangvo',
      'https://www.instagram.com/virtualoffice.joglosemar/',
      'https://linkedin.com/company/gemilangvo'
    ],
    address: {
      '@type': 'PostalAddress',
      streetAddress: 'Jl. Sudirman No. 123',
      addressLocality: 'Jakarta',
      addressRegion: 'DKI Jakarta',
      postalCode: '10220',
      addressCountry: 'ID'
    }
  }
}

/**
 * Generate Blog Schema untuk blog listing page
 * @param {Array} articles - Array of blog posts
 * @returns {Object} Blog schema
 */
export function generateBlogSchema(articles) {
  const baseUrl = process.env.NUXT_PUBLIC_SITE_URL || 'https://sewavirtualoffice.id/'
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    name: 'Gemilang VO Blog',
    description: 'Artikel dan tips terbaru seputar bisnis, virtual office, dan perkembangan dunia usaha di Indonesia',
    url: `${baseUrl}/blog`,
    publisher: {
      '@type': 'Organization',
      name: 'Gemilang VO',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/logo.png`
      }
    },
    blogPost: articles.slice(0, 10).map(article => ({
      '@type': 'BlogPosting',
      headline: article.title,
      description: article.description,
      url: `${baseUrl}/blog/${article.slug}`,
      datePublished: article.publishedAt,
      author: {
        '@type': 'Person',
        name: article.author?.name || 'Gemilang VO Team'
      }
    }))
  }
}

/**
 * Generate FAQ Schema jika artikel memiliki FAQ
 * @param {Array} faqs - Array of FAQ objects
 * @returns {Object} FAQ schema
 */
export function generateFAQSchema(faqs) {
  if (!faqs || faqs.length === 0) return null
  
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  }
}

/**
 * Estimate word count from HTML content
 * @param {string} htmlContent - HTML content
 * @returns {number} Estimated word count
 */
function estimateWordCount(htmlContent) {
  if (!htmlContent) return 0
  
  // Remove HTML tags and count words
  const textContent = htmlContent.replace(/<[^>]*>/g, ' ')
  const words = textContent.trim().split(/\s+/).filter(word => word.length > 0)
  return words.length
}

/**
 * Generate complete SEO meta tags
 * @param {Object} article - Blog post object
 * @param {string} canonicalUrl - Canonical URL
 * @returns {Object} Complete meta tags object
 */
export function generateSEOMeta(article, canonicalUrl) {
  const baseUrl = process.env.NUXT_PUBLIC_SITE_URL || 'https://sewavirtualoffice.id/'
  
  return {
    title: article.seo?.metaTitle || `${article.title} | Gemilang VO Blog`,
    meta: [
      // Basic meta tags
      { name: 'description', content: article.seo?.metaDescription || article.description },
      { name: 'keywords', content: article.seo?.keywords || article.tags?.join(', ') },
      { name: 'author', content: article.author?.name || 'Gemilang VO Team' },
      { name: 'robots', content: article.seo?.noIndex ? 'noindex,nofollow' : 'index,follow' },
      
      // Open Graph tags
      { property: 'og:type', content: article.seo?.ogType || 'article' },
      { property: 'og:title', content: article.seo?.metaTitle || article.title },
      { property: 'og:description', content: article.seo?.metaDescription || article.description },
      { property: 'og:image', content: article.featuredImage },
      { property: 'og:url', content: canonicalUrl },
      { property: 'og:site_name', content: 'Gemilang VO' },
      { property: 'og:locale', content: 'id_ID' },
      
      // Article specific OG tags
      { property: 'article:published_time', content: article.publishedAt },
      { property: 'article:modified_time', content: article.updatedAt || article.publishedAt },
      { property: 'article:author', content: article.author?.name || 'Gemilang VO Team' },
      { property: 'article:section', content: article.category },
      ...(article.tags || []).map(tag => ({ property: 'article:tag', content: tag })),
      
      // Twitter Card tags
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: article.seo?.metaTitle || article.title },
      { name: 'twitter:description', content: article.seo?.metaDescription || article.description },
      { name: 'twitter:image', content: article.featuredImage },
      { name: 'twitter:site', content: '@gemilangvo' },
      { name: 'twitter:creator', content: '@gemilangvo' }
    ],
    link: [
      { rel: 'canonical', href: canonicalUrl }
    ]
  }
}

import fs from 'fs'
import path from 'path'

export default defineEventHandler(async (event) => {
  try {
    // Read the JSON file
    const filePath = path.join(process.cwd(), 'data', 'perpajakan.json')
    const fileContent = fs.readFileSync(filePath, 'utf-8')
    const data = JSON.parse(fileContent)
    
    console.log('API Tax Services Data loaded successfully')
    
    return data
  } catch (error) {
    console.error('Error loading tax services data:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to load tax services data'
    })
  }
})

<template>
  <section v-if="locationInfo" class="py-16">
    <div class="container mx-auto px-4">
      <div class="bg-white rounded-xl shadow-md overflow-hidden p-8">
        <h2 v-if="locationInfo.heading" class="text-2xl font-bold mb-4">{{ locationInfo.heading }}</h2>
        <p v-if="locationInfo.address" class="text-gray-600 mb-6">{{ locationInfo.address }}</p>

        <div class="grid md:grid-cols-1 lg:grid-cols-2 gap-8 items-start">
          <!-- Image Slider -->
          <div v-if="roomImages && roomImages.length" class="mb-6">
            <h3 class="text-lg font-semibold mb-3"><PERSON><PERSON></h3>
            <div class="relative">
              <div class="image-slider overflow-hidden rounded-lg shadow-lg">
                <div class="slider-container flex transition-transform duration-300" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                  <div v-for="(image, index) in roomImages" :key="index" class="slider-slide w-full flex-shrink-0">
                    <img :src="image.url" :alt="image.alt || 'Room image'" class="w-full h-64 md:h-72 lg:h-80 xl:h-96 object-cover">
                  </div>
                </div>
              </div>

              <!-- Navigation Arrows -->
              <button
                @click="prevSlide"
                class="absolute left-3 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-3 rounded-full shadow-lg z-10 transition-all duration-200 hover:scale-110"
                :disabled="currentSlide === 0"
                :class="{ 'opacity-50 cursor-not-allowed': currentSlide === 0 }"
              >
                <Icon name="heroicons:chevron-left" size="24" />
              </button>

              <button
                @click="nextSlide"
                class="absolute right-3 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-3 rounded-full shadow-lg z-10 transition-all duration-200 hover:scale-110"
                :disabled="currentSlide === roomImages.length - 1"
                :class="{ 'opacity-50 cursor-not-allowed': currentSlide === roomImages.length - 1 }"
              >
                <Icon name="heroicons:chevron-right" size="24" />
              </button>

              <!-- Dots Indicator -->
              <div class="flex justify-center mt-4 space-x-2">
                <button
                  v-for="(_, index) in roomImages"
                  :key="index"
                  @click="goToSlide(index)"
                  class="w-3 h-3 rounded-full transition-all duration-200 hover:scale-125"
                  :class="currentSlide === index ? 'bg-primary-600 shadow-lg' : 'bg-gray-300 hover:bg-gray-400'"
                ></button>
              </div>
            </div>
          </div>

          <!-- Facilities -->
          <div v-if="locationInfo.facilities && locationInfo.facilities.length" class="mb-6">
            <h3 v-if="locationInfo.facilitiesHeading" class="text-lg font-semibold mb-4">{{ locationInfo.facilitiesHeading }}</h3>
            <div class="bg-gray-50 rounded-lg p-6">
              <ul class="space-y-3">
                <li v-for="facility in locationInfo.facilities" :key="facility" class="flex items-start group">
                  <Icon name="heroicons:check-circle" class="text-primary-500 mr-3 mt-0.5 flex-shrink-0 group-hover:text-primary-600 transition-colors duration-200" size="20" />
                  <span class="text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-200">{{ facility }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div v-if="locationInfo.mapUrl" class="bg-gray-100 rounded-lg p-4 mt-6">
          <iframe
            :src="locationInfo.mapUrl"
            width="100%"
            height="300"
            style="border:0;"
            allowfullscreen=""
            loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"
            class="rounded-lg"
          ></iframe>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// Assuming AppButton and Icon are globally registered or imported if needed
// import AppButton from '~/components/atoms/AppButton.vue';
import { ref, watch } from 'vue';

const props = defineProps({
  locationInfo: {
    type: Object,
    default: () => ({})
  },
  roomImages: {
    type: Array,
    default: () => []
  }
});

// Image slider logic
const currentSlide = ref(0);

// Watch for changes in roomImages and reset slide if needed
watch(() => props.roomImages, (newImages) => {
  if (newImages && newImages.length > 0) {
    // Reset to first slide if current slide is out of bounds
    if (currentSlide.value >= newImages.length) {
      currentSlide.value = 0;
    }
  } else {
    // Reset to 0 if no images
    currentSlide.value = 0;
  }
}, { immediate: true });

const nextSlide = () => {
  if (props.roomImages && currentSlide.value < props.roomImages.length - 1) {
    currentSlide.value++;
  }
};

const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  }
};

const goToSlide = (index) => {
  if (props.roomImages && index >= 0 && index < props.roomImages.length) {
    currentSlide.value = index;
  }
};
</script>
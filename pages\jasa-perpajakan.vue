<template>
  <div>
    <!-- Hero Section -->
    <HeroSectionWithStats
      v-if="heroSectionData"
      :backgroundImageUrl="heroSectionData.backgroundImageUrl"
      :headline="heroSectionData.headline"
      :subheadline="heroSectionData.subheadline"
      :primaryButtonText="heroSectionData.primaryButtonText"
      :primaryButtonLink="heroSectionData.primaryButtonLink"
      :primaryButtonWhatsapp="heroSectionData.primaryButtonWhatsapp"
      :secondaryButtonText="heroSectionData.secondaryButtonText"
      :secondaryButtonLink="heroSectionData.secondaryButtonLink"
      :stats="heroSectionData.stats"
    />

    <!-- Why Choose Section -->
    <WhyChooseSection
      v-if="whyChooseData"
      :heading="whyChooseData.heading"
      :subheading="whyChooseData.subheading"
      :features="whyChooseData.features"
    />

    <!-- Services Section -->
    <ServicesGridSection
      v-if="servicesSectionData"
      sectionId="services"
      badgeText="Layanan Ka<PERSON>"
      :heading="servicesSectionData.heading"
      :subheading="servicesSectionData.subheading"
      :services="servicesSectionData.services"
      buttonText="Pelajari Lebih Lanjut"
    />

    <!-- Process Section -->
    <ProcessStepsSection
      v-if="processSectionData"
      badgeText="Proses Kerja"
      :heading="processSectionData.heading"
      :subheading="processSectionData.subheading"
      :steps="processSectionData.steps"
    />

    <!-- Tax Reports Section -->
    <TaxReportsSection
      v-if="featuresSectionData"
      badgeText="Kepatuhan Pajak"
      :heading="featuresSectionData.heading"
      :subheading="featuresSectionData.subheading"
      :features="featuresSectionData.features"
    />



    <!-- Pricing Section -->
    <PricingPlansSection
      v-if="pricingSectionData"
      badgeText="Harga Layanan"
      :heading="pricingSectionData.heading"
      :subheading="pricingSectionData.subheading"
      :plans="pricingSectionData.plans"
      buttonText="Pilih Paket"
      whatsapp-number="6287799088880"
      service-name="Jasa Perpajakan"
    />

    <!-- Testimonials Section -->
    <TestimonialsSection
      v-if="testimonialsSectionData"
      :heading="testimonialsSectionData.heading"
      :subheading="testimonialsSectionData.subheading"
      :testimonials="testimonialsSectionData.testimonials"
    />

    <!-- FAQ Section -->
    <FaqSection
      v-if="faqSectionData"
      :heading="faqSectionData.heading"
      :subheading="faqSectionData.subheading"
      :faqs="faqSectionData.faqs"
    />

    <!-- CTA Section -->
    <CtaContactSection
      v-if="ctaSectionData"
      :heading="ctaSectionData.heading"
      :subheading="ctaSectionData.subheading"
      :benefits="ctaSectionData.benefits"
      :button-text="ctaSectionData.primaryButtonText"
      whatsapp-link="https://wa.me/6287799088880?text=Halo,%20saya%20tertarik%20dengan%20layanan%20Perpajakan"
      :form-heading="ctaSectionData.formHeading"
      :operational-hours="ctaSectionData.operationalHours"
      :contact-info="ctaSectionData.contactInfo"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import HeroSectionWithStats from '~/components/HeroSectionWithStats.vue'
import WhyChooseSection from '~/components/WhyChooseSection.vue'
import ServicesGridSection from '~/components/ServicesGridSection.vue'
import ProcessStepsSection from '~/components/ProcessStepsSection.vue'
import TaxReportsSection from '~/components/TaxReportsSection.vue'
import PricingPlansSection from '~/components/PricingPlansSection.vue'
import TestimonialsSection from '~/components/TestimonialsSection.vue'
import FaqSection from '~/components/FaqSection.vue'
import CtaContactSection from '~/components/CtaContactSection.vue'

// Load data from API
const { data: taxServicesData } = await useFetch('/api/tax-services')

// For debugging
console.log('API Data:', taxServicesData.value)

// Use API data directly
const pageData = computed(() => taxServicesData.value || {})

// Extract section data
const heroSectionData = computed(() => pageData.value.heroSection)
const whyChooseData = computed(() => pageData.value.whyChoose)
const servicesSectionData = computed(() => pageData.value.servicesSection)
const processSectionData = computed(() => pageData.value.processSection)
const featuresSectionData = computed(() => pageData.value.featuresSection)
const pricingSectionData = computed(() => pageData.value.pricingSection)
const testimonialsSectionData = computed(() => pageData.value.testimonialsSection)
const faqSectionData = computed(() => pageData.value.faqSection)
const ctaSectionData = computed(() => pageData.value.ctaSection)

// SEO Configuration
import { generateServiceSchema, generateWebPageSchema } from '~/utils/seo.js'

const config = useRuntimeConfig()
const baseUrl = config.public.siteUrl.replace(/\/$/, '') // Remove trailing slash
const pageUrl = `${baseUrl}/jasa-perpajakan`

const pageSchemas = computed(() => [
  generateServiceSchema(
    'Jasa Perpajakan Professional',
    'Layanan konsultasi dan pelaporan pajak profesional untuk bisnis. Dikerjakan oleh konsultan pajak bersertifikasi dengan pendekatan yang efisien.',
    baseUrl
  ),
  generateWebPageSchema(
    'Jasa Perpajakan Professional | Gemilang VO',
    'Layanan konsultasi dan pelaporan pajak profesional untuk bisnis. Dikerjakan oleh konsultan pajak bersertifikasi dengan pendekatan yang efisien.',
    pageUrl,
    baseUrl
  )
])

useHead({
  title: 'Jasa Perpajakan Professional | Gemilang VO',
  meta: [
    { name: 'description', content: 'Layanan konsultasi dan pelaporan pajak profesional untuk bisnis. Dikerjakan oleh konsultan pajak bersertifikasi dengan pendekatan yang efisien.' },
    { name: 'keywords', content: 'jasa perpajakan, konsultan pajak, pelaporan pajak, SPT, PPh, PPN, konsultasi pajak, tax consultant' },
    { name: 'robots', content: 'index,follow' },
    { property: 'og:title', content: 'Jasa Perpajakan Professional | Gemilang VO' },
    { property: 'og:description', content: 'Layanan konsultasi dan pelaporan pajak profesional untuk bisnis. Dikerjakan oleh konsultan pajak bersertifikasi dengan pendekatan yang efisien.' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: pageUrl },
    { property: 'og:site_name', content: 'Gemilang VO' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'Jasa Perpajakan Professional | Gemilang VO' },
    { name: 'twitter:description', content: 'Layanan konsultasi dan pelaporan pajak profesional untuk bisnis. Dikerjakan oleh konsultan pajak bersertifikasi dengan pendekatan yang efisien.' }
  ],
  link: [
    { rel: 'canonical', href: pageUrl }
  ],
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify(pageSchemas.value)
    }
  ]
})
</script>
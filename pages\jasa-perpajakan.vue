<template>
  <div>
    <!-- Hero Section -->
    <div class="relative h-[85vh] flex items-center">
      <div class="absolute inset-0 z-0">
        <img 
          src="https://images.pexels.com/photos/6863250/pexels-photo-6863250.jpeg" 
          alt="Jasa Perpajakan" 
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-r from-primary-900/95 to-black/80"></div>
      </div>
      
      <div class="relative z-10 container mx-auto px-4">
        <div class="max-w-3xl">
          <span class="inline-block bg-white/10 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
            Ko<PERSON><PERSON><PERSON>
          </span>
          <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Sol<PERSON><PERSON> <span class="text-primary-300">Modern & Efisien</span>
          </h1>
          <p class="text-xl text-gray-200 mb-8 leading-relaxed">
            Optimalkan kepatuhan pajak bisnis Anda dengan dukungan konsultan pajak berpengalaman
          </p>
          <div class="flex flex-wrap gap-4">
            <AppButton 
              size="lg"
              href="https://wa.me/6287799088880?text=Halo,%20saya%20tertarik%20dengan%20layanan%20Perpajakan"
              whatsapp
            >
              Konsultasi Gratis
            </AppButton>
            <AppButton 
              size="lg" 
              variant="outline"
              class="text-white border-white hover:bg-white/10"
            >
              Lihat Layanan
            </AppButton>
          </div>
        </div>
      </div>

      <!-- Tax Stats -->
      <div class="absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-md py-8">
        <div class="container mx-auto px-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center">
              <div class="text-4xl font-bold text-white mb-2">2000+</div>
              <div class="text-gray-300">SPT Terlapor</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-white mb-2">98%</div>
              <div class="text-gray-300">Kepatuhan</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-white mb-2">15+</div>
              <div class="text-gray-300">Tahun Pengalaman</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-white mb-2">24/7</div>
              <div class="text-gray-300">Dukungan</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Services Section -->
    <section class="py-24">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-16">
          <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            Layanan Kami
          </span>
          <h2 class="text-4xl font-bold mb-6">Solusi Perpajakan Lengkap</h2>
          <p class="text-gray-600 text-lg">
            Layanan perpajakan yang disesuaikan dengan kebutuhan bisnis Anda
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div v-for="(service, index) in services" :key="index" 
              class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div class="w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-primary-600 transition-colors duration-300">
              <Icon :name="service.icon" class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" />
            </div>
            <h3 class="text-xl font-bold mb-4">{{ service.name }}</h3>
            <p class="text-gray-600 mb-6">{{ service.description }}</p>
            <ul class="space-y-3">
              <li v-for="feature in service.features" :key="feature" class="flex items-start">
                <Icon name="heroicons:check-circle" class="text-primary-500 w-5 h-5 mr-2 mt-1 flex-shrink-0" />
                <span class="text-gray-700">{{ feature }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Tax Compliance Section -->
    <section class="py-24 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-16">
          <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            Kepatuhan Pajak
          </span>
          <h2 class="text-4xl font-bold mb-6">Jenis Pelaporan Pajak</h2>
          <p class="text-gray-600 text-lg">
            Layanan pelaporan pajak sesuai ketentuan perpajakan
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div v-for="(report, index) in taxReports" :key="index" 
              class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
            <div class="flex items-start mb-6">
              <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                <Icon :name="report.icon" class="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <h3 class="text-xl font-bold mb-2">{{ report.title }}</h3>
                <p class="text-gray-600">{{ report.description }}</p>
              </div>
            </div>
            <ul class="space-y-3">
              <li v-for="item in report.items" :key="item" class="flex items-start">
                <Icon name="heroicons:check-circle" class="text-primary-500 w-5 h-5 mr-2 mt-1" />
                <span class="text-gray-700">{{ item }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-24">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-16">
          <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            Keunggulan
          </span>
          <h2 class="text-4xl font-bold mb-6">Mengapa Memilih Kami</h2>
          <p class="text-gray-600 text-lg">
            Keunggulan layanan perpajakan kami
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div v-for="(feature, index) in features" :key="index" 
              class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
            <div class="w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center mb-6">
              <Icon :name="feature.icon" class="w-8 h-8 text-primary-600" />
            </div>
            <h3 class="text-xl font-bold mb-4">{{ feature.title }}</h3>
            <p class="text-gray-600">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-24 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-16">
          <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            Harga
          </span>
          <h2 class="text-4xl font-bold mb-6">Paket Layanan</h2>
          <p class="text-gray-600 text-lg">
            Pilih paket yang sesuai dengan kebutuhan bisnis Anda
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div v-for="(plan, index) in pricingPlans" :key="index" 
              class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
              :class="{'border-2 border-primary-500': plan.popular}">
            <div class="p-8" :class="{'bg-primary-500 text-white': plan.popular}">
              <h3 class="text-2xl font-bold mb-2">{{ plan.name }}</h3>
              <p class="opacity-90 mb-4">{{ plan.description }}</p>
              <div class="text-3xl font-bold mb-2">{{ plan.price }}</div>
              <p class="text-sm opacity-75">{{ plan.period }}</p>
            </div>
            <div class="p-8">
              <ul class="space-y-4 mb-8">
                <li v-for="feature in plan.features" :key="feature" class="flex items-start">
                  <Icon name="heroicons:check-circle" class="text-primary-500 w-5 h-5 mr-3" />
                  <span>{{ feature }}</span>
                </li>
              </ul>
              <AppButton 
                :variant="plan.popular ? 'primary' : 'outline'"
                class="w-full"
              >
                Pilih Paket
              </AppButton>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Process Section -->
    <section class="py-24">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-16">
          <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            Proses
          </span>
          <h2 class="text-4xl font-bold mb-6">Bagaimana Kami Bekerja</h2>
          <p class="text-gray-600 text-lg">
            Proses penanganan perpajakan yang terstruktur
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div v-for="(step, index) in steps" :key="index" class="relative">
            <div class="bg-white rounded-2xl p-8 shadow-lg h-full">
              <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold mb-6">
                {{ index + 1 }}
              </div>
              <h3 class="text-xl font-bold mb-4">{{ step.title }}</h3>
              <p class="text-gray-600">{{ step.description }}</p>
            </div>
            <!-- Connector Line -->
            <div v-if="index < steps.length - 1" class="hidden md:block absolute top-1/2 -right-4 w-8 border-t-2 border-dashed border-primary-300"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-24 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-16">
          <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            FAQ
          </span>
          <h2 class="text-4xl font-bold mb-6">Pertanyaan Umum</h2>
          <p class="text-gray-600 text-lg">
            Temukan jawaban atas pertanyaan yang sering diajukan
          </p>
        </div>

        <div class="max-w-3xl mx-auto">
          <div class="space-y-4">
            <div v-for="(faq, index) in faqs" :key="index"
                class="bg-white rounded-xl overflow-hidden">
              <button 
                class="w-full flex items-center justify-between p-6 text-left"
                @click="activeFaq = activeFaq === index ? null : index"
              >
                <span class="font-semibold text-lg">{{ faq.question }}</span>
                <Icon 
                  :name="activeFaq === index ? 'heroicons:minus-circle' : 'heroicons:plus-circle'"
                  class="w-6 h-6 text-primary-600"
                />
              </button>
              <div v-show="activeFaq === index" class="px-6 pb-6">
                <p class="text-gray-600">{{ faq.answer }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-24">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto bg-gradient-to-r from-primary-600 to-primary-800 rounded-2xl overflow-hidden shadow-xl text-white">
          <div class="p-12 text-center">
            <h2 class="text-3xl font-bold mb-6">
              Optimalkan Perpajakan Bisnis Anda
            </h2>
            <p class="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
              Konsultasikan kebutuhan perpajakan bisnis Anda dengan tim ahli kami dan dapatkan solusi terbaik
            </p>
            <div class="flex flex-wrap justify-center gap-4">
              <AppButton 
                size="lg"
                variant="secondary"
                href="https://wa.me/6287799088880?text=Halo,%20saya%20tertarik%20dengan%20layanan%20Perpajakan"
                whatsapp
              >
                Konsultasi Gratis
              </AppButton>
              <AppButton 
                size="lg"
                variant="outline"
                class="text-white border-white hover:bg-white/10"
              >
                Pelajari Lebih Lanjut
              </AppButton>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
const services = [
  {
    icon: 'heroicons:document-check',
    name: 'Pelaporan Pajak',
    description: 'Layanan pelaporan pajak lengkap',
    features: [
      'SPT Masa PPh 21',
      'SPT Masa PPN',
      'SPT Tahunan Badan',
      'SPT Tahunan Pribadi'
    ]
  },
  {
    icon: 'heroicons:presentation-chart-line',
    name: 'Perencanaan Pajak',
    description: 'Strategi perpajakan yang efisien',
    features: [
      'Analisis Beban Pajak',
      'Optimasi Pajak Legal',
      'Rekomendasi Strategi',
      'Review Kebijakan'
    ]
  },
  {
    icon: 'heroicons:chat-bubble-left-right',
    name: 'Konsultasi Pajak',
    description: 'Konsultasi dengan ahli pajak',
    features: [
      'Konsultasi Rutin',
      'Pendampingan Audit',
      'Update Peraturan',
      'Solusi Masalah'
    ]
  }
]

const taxReports = [
  {
    icon: 'heroicons:document-chart-bar',
    title: 'PPh Badan',
    description: 'Pelaporan pajak penghasilan badan',
    items: [
      'SPT Tahunan Badan',
      'Perhitungan Pajak',
      'Rekonsiliasi Fiskal',
      'Laporan Keuangan Fiskal'
    ]
  },
  {
    icon: 'heroicons:user',
    title: 'PPh 21',
    description: 'Pajak penghasilan karyawan',
    items: [
      'Perhitungan PPh 21',
      'SPT Masa PPh 21',
      'Bukti Potong A1',
      'Rekap Gaji'
    ]
  },
  {
    icon: 'heroicons:building-office',
    title: 'PPN',
    description: 'Pajak Pertambahan Nilai',
    items: [
      'SPT Masa PPN',
      'e-Faktur',
      'Rekonsiliasi PPN',
      'Restitusi PPN'
    ]
  },
  {
    icon: 'heroicons:document-text',
    title: 'Pajak Lainnya',
    description: 'Berbagai jenis pajak lainnya',
    items: [
      'PPh 23/26',
      'PPh 4(2)',
      'PBB',
      'Pajak Daerah'
    ]
  }
]

const features = [
  {
    icon: 'heroicons:user-group',
    title: 'Konsultan Ahli',
    description: 'Tim konsultan pajak bersertifikasi dan berpengalaman'
  },
  {
    icon: 'heroicons:shield-check',
    title: 'Kepatuhan',
    description: 'Jaminan kepatuhan terhadap peraturan perpajakan terkini'
  },
  {
    icon: 'heroicons:clock',
    title: 'Tepat Waktu',
    description: 'Pelaporan pajak selalu tepat waktu sesuai ketentuan'
  },
  {
    icon: 'heroicons:document-magnifying-glass',
    title: 'Update',
    description: 'Selalu update dengan peraturan perpajakan terbaru'
  }
]

const pricingPlans = [
  {
    name: 'Basic',
    description: 'Untuk UMKM',
    price: 'Rp 2 Juta',
    period: 'per bulan',
    features: [
      'SPT Masa PPh 21',
      'SPT Masa PPN',
      'Konsultasi Basic',
      'Update Peraturan'
    ]
  },
  {
    name: 'Business',
    description: 'Untuk bisnis menengah',
    price: 'Rp 5 Juta',
    period: 'per bulan',
    popular: true,
    features: [
      'Semua fitur Basic',
      'SPT Tahunan Badan',
      'Perencanaan Pajak',
      'Konsultasi Premium',
      'Review Bulanan'
    ]
  },
  {
    name: 'Enterprise',
    description: 'Untuk korporasi',
    price: 'Custom',
    period: 'Hubungi kami',
    features: [
      'Solusi Custom',
      'Dedicated Consultant',
      'Tax Review',
      'Tax Planning',
      'Priority Support'
    ]
  }
]

const steps = [
  {
    title: 'Konsultasi',
    description: 'Diskusi kebutuhan dan analisis perpajakan'
  },
  {
    title: 'Pengumpulan Data',
    description: 'Pengumpulan dokumen dan data perpajakan'
  },
  {
    title: 'Pemrosesan',
    description: 'Perhitungan dan penyusunan laporan pajak'
  },
  {
    title: 'Pelaporan',
    description: 'Pelaporan pajak dan dokumentasi'
  }
]

const faqs = [
  {
    question: 'Berapa lama proses pelaporan pajak?',
    answer: 'Waktu pelaporan bervariasi tergantung jenis pajak, umumnya 3-5 hari kerja untuk pelaporan rutin.'
  },
  {
    question: 'Apakah ada jaminan kepatuhan?',
    answer: 'Ya, kami menjamin kepatuhan terhadap peraturan perpajakan dan bertanggung jawab atas perhitungan yang dilakukan.'
  },
  {
    question: 'Bagaimana dengan kerahasiaan data?',
    answer: 'Kami menerapkan standar kerahasiaan tinggi dan semua konsultan menandatangani NDA.'
  },
  {
    question: 'Apakah termasuk pendampingan pemeriksaan?',
    answer: 'Ya, untuk paket Business dan Enterprise termasuk pendampingan dalam hal pemeriksaan pajak.'
  }
]

const activeFaq = ref(null)

useHead({
  title: 'Jasa Perpajakan Professional | Gemilang VO',
  meta: [
    { name: 'description', content: 'Layanan konsultasi dan pelaporan pajak profesional untuk bisnis. Dikerjakan oleh konsultan pajak bersertifikasi dengan pendekatan yang efisien.' }
  ]
})
</script>
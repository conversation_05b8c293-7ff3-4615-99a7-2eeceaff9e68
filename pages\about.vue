<template>
  <div>
    <!-- About Hero -->
    <div class="relative">
      <div class="absolute inset-0 z-0">
        <img 
          src="https://images.pexels.com/photos/3183183/pexels-photo-3183183.jpeg" 
          alt="About Us" 
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-r from-primary-900/90 to-black/70"></div>
      </div>
      
      <div class="relative z-10 pt-32 pb-20">
        <div class="container mx-auto px-4">
          <div class="max-w-3xl animate-slide-up">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
              About Gemilang VO
            </h1>
            <p class="text-xl text-gray-200 mb-6">
              Supporting businesses with premium virtual office solutions since 2018
            </p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Our Story -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto">
          <h2 class="text-3xl font-bold mb-6">Our Story</h2>
          <p class="text-gray-700 mb-4">
            Founded in 2018, Gemilang VO was born from a simple observation: many businesses were paying excessive rent for office spaces they barely used. Our founder, having experienced the challenges of starting a business with limited capital, recognized the need for affordable yet professional business solutions.
          </p>
          <p class="text-gray-700 mb-4">
            What began as a small operation in Jakarta has grown into a nationwide network of virtual offices across Indonesia's major business centers. Today, we proudly serve over 500 businesses ranging from solo entrepreneurs to established companies looking to expand their presence without the overhead of physical offices.
          </p>
          <p class="text-gray-700">
            Our mission remains unchanged: to provide affordable, professional business solutions that allow entrepreneurs to focus on growth rather than infrastructure.
          </p>
        </div>
      </div>
    </section>
    
    <!-- Our Values -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <SectionHeading 
          title="Our Values"
          subtitle="The principles that guide everything we do"
        />
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div v-for="(value, index) in values" :key="index" class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300">
            <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 mb-4">
              <Icon :name="value.icon" size="24" />
            </div>
            <h3 class="text-xl font-semibold mb-2">{{ value.title }}</h3>
            <p class="text-gray-600">{{ value.description }}</p>
          </div>
        </div>
      </div>
    </section>
    
    <!-- Team Section -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <SectionHeading 
          title="Our Team"
          subtitle="Meet the people dedicated to your business success"
        />
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div v-for="(member, index) in team" :key="index" class="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-all duration-300">
            <div class="aspect-square bg-gray-200">
              <!-- In a real implementation, you would use actual team member photos -->
              <div class="w-full h-full bg-primary-100 flex items-center justify-center">
                <span class="text-5xl font-bold text-primary-300">{{ member.name[0] }}</span>
              </div>
            </div>
            <div class="p-4 text-center">
              <h3 class="font-semibold text-lg">{{ member.name }}</h3>
              <p class="text-primary-600 text-sm mb-2">{{ member.position }}</p>
              <div class="flex justify-center space-x-3 mt-3">
                <a href="#" class="text-gray-500 hover:text-primary-600">
                  <Icon name="mdi:linkedin" size="20" />
                </a>
                <a href="#" class="text-gray-500 hover:text-primary-600">
                  <Icon name="mdi:email" size="20" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- Milestones -->
    <section class="py-16 bg-primary-900 text-white">
      <div class="container mx-auto px-4">
        <SectionHeading 
          title="Our Journey"
          subtitle="Key milestones in our growth story"
          light
        />
        
        <div class="relative">
          <!-- Timeline Line -->
          <div class="hidden md:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-primary-700 transform -translate-x-1/2"></div>
          
          <div class="space-y-12">
            <div v-for="(milestone, index) in milestones" :key="index" 
                class="relative md:flex items-center justify-between">
              <div class="md:w-5/12" :class="{ 'md:text-right': index % 2 === 0 }">
                <div class="bg-primary-800 p-6 rounded-lg mb-8 md:mb-0" 
                    :class="{ 'ml-auto mr-0 md:mr-8': index % 2 === 0, 'md:ml-8': index % 2 !== 0 }">
                  <span class="text-primary-300 font-bold">{{ milestone.year }}</span>
                  <h3 class="text-xl font-bold mt-1 mb-2">{{ milestone.title }}</h3>
                  <p class="text-primary-200">{{ milestone.description }}</p>
                </div>
              </div>
              
              <div class="absolute flex md:left-1/2 md:transform md:-translate-x-1/2">
                <div class="w-10 h-10 bg-primary-300 rounded-full flex items-center justify-center z-10">
                  <div class="w-6 h-6 bg-primary-600 rounded-full"></div>
                </div>
              </div>
              
              <div class="md:w-5/12" :class="{ 'md:order-first': index % 2 !== 0 }"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- Contact CTA -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto bg-white shadow-xl rounded-xl overflow-hidden">
          <div class="md:flex">
            <div class="md:w-1/2 p-8 md:p-10">
              <h2 class="text-3xl font-bold mb-4">Get in Touch</h2>
              <p class="text-gray-600 mb-6">Have questions about our services? We're here to help you find the perfect virtual office solution for your business.</p>
              
              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="mr-3 text-primary-600">
                    <Icon name="heroicons:map-pin" />
                  </div>
                  <p>Jl. Gatot Subroto No. 123<br>Jakarta, Indonesia</p>
                </div>
                
                <div class="flex items-center">
                  <div class="mr-3 text-primary-600">
                    <Icon name="heroicons:phone" />
                  </div>
                  <p>+62 21 1234 5678</p>
                </div>
                
                <div class="flex items-center">
                  <div class="mr-3 text-primary-600">
                    <Icon name="heroicons:envelope" />
                  </div>
                  <p>info@Gemilang VO.id</p>
                </div>
              </div>
              
              <div class="mt-8">
                <AppButton>Contact Us</AppButton>
              </div>
            </div>
            
            <div class="md:w-1/2 bg-gray-800">
              <div class="w-full h-full">
                <!-- In a real implementation, you would embed a map here -->
                <div class="flex items-center justify-center h-full p-10 text-white">
                  <p>Map would be displayed here</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
const values = [
  {
    icon: 'heroicons:star',
    title: 'Excellence',
    description: 'We strive for excellence in every service we provide, ensuring the highest standards of professionalism and quality.'
  },
  {
    icon: 'heroicons:user-group',
    title: 'Customer-Centric',
    description: 'Our clients\' success is our success. We tailor our services to meet the unique needs of each business we serve.'
  },
  {
    icon: 'heroicons:light-bulb',
    title: 'Innovation',
    description: 'We continuously evolve our services to incorporate the latest technologies and business practices.'
  },
  {
    icon: 'heroicons:currency-dollar',
    title: 'Value',
    description: 'We provide premium services at competitive prices, ensuring excellent return on investment for our clients.'
  },
  {
    icon: 'heroicons:shield-check',
    title: 'Integrity',
    description: 'We operate with transparency and honesty in all our business dealings and client relationships.'
  },
  {
    icon: 'heroicons:hand-raised',
    title: 'Support',
    description: 'We\'re committed to providing exceptional support to help our clients succeed in their business endeavors.'
  }
]

const team = [
  {
    name: 'Ahmad Rasyid',
    position: 'Founder & CEO',
  },
  {
    name: 'Dewi Pratiwi',
    position: 'Operations Director',
  },
  {
    name: 'Budi Hartono',
    position: 'Client Relations Manager',
  },
  {
    name: 'Siti Nurhaliza',
    position: 'Business Development',
  }
]

const milestones = [
  {
    year: '2018',
    title: 'Foundation',
    description: 'Gemilang VO was founded in Jakarta with our first location'
  },
  {
    year: '2019',
    title: 'Expansion',
    description: 'Opened virtual office locations in Yogyakarta and Surabaya'
  },
  {
    year: '2020',
    title: 'Service Growth',
    description: 'Added business formation services and virtual assistant offerings'
  },
  {
    year: '2021',
    title: 'Digital Transformation',
    description: 'Launched our digital platform for seamless client management'
  },
  {
    year: '2023',
    title: 'National Coverage',
    description: 'Expanded to all major Indonesian cities with 10+ locations'
  }
]

useHead({
  title: 'About Us | Gemilang VO',
  meta: [
    { name: 'description', content: 'Learn about Gemilang VO - Indonesia\'s premier provider of virtual office solutions since 2018. Our story, values, and the team behind our success.' }
  ]
})
</script>
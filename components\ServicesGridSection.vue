<template>
  <section class="py-20" :id="sectionId">
    <div class="container mx-auto px-4">
      <div class="max-w-3xl mx-auto text-center mb-16">
        <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
          {{ badgeText }}
        </span>
        <h2 class="text-3xl md:text-4xl font-bold mb-6">{{ heading }}</h2>
        <p class="text-gray-600 text-lg">
          {{ subheading }}
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div v-for="(service, index) in services" :key="index" 
            class="border-2 border-primary-200 rounded-2xl p-8 hover:shadow-lg transition-shadow duration-300">
          <div class="w-16 h-16 bg-primary-600 rounded-xl flex items-center justify-center mb-6">
            <Icon :name="service.icon" class="w-8 h-8 text-white" />
          </div>
          <h3 class="text-2xl font-bold mb-4">{{ service.name }}</h3>
          <p class="text-gray-600 mb-6">{{ service.description }}</p>
          <ul class="space-y-3 mb-8">
            <li v-for="feature in service.features" :key="feature" class="flex items-start">
              <Icon name="heroicons:check-circle" class="text-primary-500 w-5 h-5 mr-2 mt-1 flex-shrink-0" />
              <span class="text-gray-700">{{ feature }}</span>
            </li>
          </ul>
          <AppButton variant="outline" class="w-full">{{ buttonText }}</AppButton>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import AppButton from '~/components/atoms/AppButton.vue'

defineProps({
  sectionId: {
    type: String,
    default: ''
  },
  badgeText: {
    type: String,
    default: 'Layanan Kami'
  },
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  services: {
    type: Array,
    required: true,
    default: () => []
  },
  buttonText: {
    type: String,
    default: 'Pelajari Lebih Lanjut'
  }
})
</script>